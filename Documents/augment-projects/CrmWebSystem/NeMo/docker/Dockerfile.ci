# syntax=docker/dockerfile:1-labs

# Copyright (c) 2019, NVIDIA CORPORATION. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

ARG BASE_IMAGE=nvcr.io/nvidia/pytorch:24.07-py3

FROM ${BASE_IMAGE} as base-image
ENV PIP_CONSTRAINT="" 
ENV TRANSFORMERS_OFFLINE=0
ENV HYDRA_FULL_ERROR=1
ENV PYTHONUNBUFFERED=1

# APT packages
RUN <<"EOF" bash -ex
apt-get update
apt-get install -y bc libsox-fmt-all
apt-get clean
EOF

FROM base-image as te-wheel
WORKDIR /tmp/NeMo
ARG TE_REPO
ARG TE_TAG
RUN --mount=type=bind,source=docker/common/install_dep.sh,target=/tmp/NeMo/install_dep.sh \
  --mount=type=bind,source=external/patches,target=/tmp/NeMo/external/patches <<"EOF" bash -ex
  
  bash /tmp/NeMo/install_dep.sh --library te --mode build
  ls -al /tmp/Megatron-LM || true
EOF

FROM base-image as mcore-wheel
WORKDIR /tmp/NeMo
ARG MLM_REPO
ARG MLM_TAG
RUN --mount=type=bind,source=docker/common/install_dep.sh,target=/tmp/NeMo/install_dep.sh <<"EOF" bash -ex
  
  bash /tmp/NeMo/install_dep.sh --library mcore --mode build
  ls -al /tmp/Megatron-LM || true
EOF

FROM base-image 
WORKDIR /tmp/NeMo
ENV INSTALL_DIR="/opt"
RUN \  
  --mount=type=bind,from=te-wheel,source=/opt/wheels/te,target=/opt/wheels/te \
  --mount=type=bind,from=mcore-wheel,source=/opt/wheels/mcore,target=/opt/wheels/mcore \
  --mount=type=bind,source=requirements,target=/tmp/NeMo/requirements \
  --mount=type=bind,source=tools/ctc_segmentation/requirements.txt,target=/tmp/NeMo/tools/ctc_segmentation/requirements.txt \
  --mount=type=bind,source=docker/common/install_dep.sh,target=/tmp/NeMo/install_dep.sh \
  --mount=type=bind,source=setup.py,target=/tmp/NeMo/setup.py \
  --mount=type=bind,source=external/patches,target=/tmp/NeMo/external/patches \
  --mount=type=bind,source=README.md,target=/tmp/NeMo/README.md \
  --mount=type=bind,source=nemo/package_info.py,target=/tmp/NeMo/nemo/package_info.py \
  --mount=type=bind,source=nemo/__init__.py,target=/tmp/NeMo/nemo/__init__.py <<"EOF" bash -ex
    
  bash /tmp/NeMo/install_dep.sh --library te --mode install
  bash /tmp/NeMo/install_dep.sh --library mcore --mode install
  bash /tmp/NeMo/install_dep.sh --library extra --mode install
  pip install --no-cache-dir ".[all]"
  rm -rf $NEMO_DIR || true
EOF


WORKDIR /workspace
ENV PYTHONPATH="${PYTHONPATH}:/workspace/Megatron-LM"
ENV NEMO_HOME="/home/<USER>/nemo_home"

ARG IMAGE_LABEL
LABEL "nemo.library"=${IMAGE_LABEL}

ARG PR_NUMBER
LABEL "nemo.pr_number"=${PR_NUMBER}
