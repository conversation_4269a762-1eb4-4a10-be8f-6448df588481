=========
NeMo APIs
=========

**NOTE: This page is intended for NeMo 1.0 features only.**

You can learn more about the underlying principles of the NeMo codebase in this section.

The `NeMo Framework codebase <https://github.com/NVIDIA/NeMo>`__ is composed of a `core <https://github.com/NVIDIA/NeMo/tree/main/nemo/core>`__ section which contains the main building blocks of the framework, and various `collections <https://github.com/NVIDIA/NeMo/tree/main/nemo/collections>`__ which help you
build specialized AI models.

You can learn more about aspects of the NeMo "core" by following the links below:

.. toctree::
   :maxdepth: 1
   :name: core
   :titlesonly:

   core/core
   core/neural_modules
   core/exp_manager
   core/neural_types
   core/export
   core/adapters/intro

You can learn more about aspects of the NeMo APIs by following the links below:

.. toctree::
   :maxdepth: 1
   :name: API
   :titlesonly:

   core/api
   common/intro
   asr/api
   tts/api


Alternatively, you can jump straight to the documentation for the individual collections:

* :doc:`Automatic Speech Recognition (ASR) <../asr/intro>`

* :doc:`Text-to-Speech (TTS) <../tts/intro>`

