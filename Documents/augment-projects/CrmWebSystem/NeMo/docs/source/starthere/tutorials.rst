.. _tutorials:

Tutorials
=========

The best way to get started with NeMo is to start with one of our tutorials. These tutorials cover various domains and provide both introductory and advanced topics. They are designed to help you understand and use the NeMo toolkit effectively.

Running Tutorials on Colab
--------------------------

Most NeMo tutorials can be run on `Google's Colab <https://colab.research.google.com/notebooks/intro.ipynb>`_.

To run a tutorial:

1. Click the **Colab** link associated with the tutorial you are interested in from the table below.
2. Once in Colab, connect to an instance with a GPU by clicking **Runtime** > **Change runtime type** and selecting **GPU** as the hardware accelerator.

Tutorial Overview
-----------------

.. list-table:: **General Tutorials**
   :widths: 15 25 60
   :header-rows: 1

   * - Domain
     - Title
     - GitHub URL
   * - General
     - Getting Started: NeMo Fundamentals
     - `NeMo Fundamentals <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/00_NeMo_Primer.ipynb>`_
   * - General
     - Getting Started: Audio translator example
     - `Audio translator example <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/AudioTranslationSample.ipynb>`_
   * - General
     - Getting Started: Voice swap example
     - `Voice swap example <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/VoiceSwapSample.ipynb>`_
   * - General
     - Getting Started: NeMo Models
     - `NeMo Models <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/01_NeMo_Models.ipynb>`_
   * - General
     - Getting Started: NeMo Adapters
     - `NeMo Adapters <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/02_NeMo_Adapters.ipynb>`_
   * - General
     - Getting Started: NeMo Models on Hugging Face Hub
     - `NeMo Models on HF Hub <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/Publish_NeMo_Model_On_Hugging_Face_Hub.ipynb>`_

.. list-table:: **Multimodal Tutorials**
   :widths: 20 25 55
   :header-rows: 1

   * - Domain
     - Title
     - GitHub URL
   * - Multimodal
     - Preparations and Advanced Applications: Multimodal Data Preparation
     - `Multimodal Data Preparation <https://github.com/NVIDIA/NeMo/blob/main/tutorials/multimodal/Multimodal%20Data%20Preparation.ipynb>`_
   * - Multimodal
     - Preparations and Advanced Applications: NeVA (LLaVA) Tutorial
     - `NeVA (LLaVA) Tutorial <https://github.com/NVIDIA/NeMo/blob/main/tutorials/multimodal/NeVA%20Tutorial.ipynb>`_
   * - Multimodal
     - Preparations and Advanced Applications: Stable Diffusion Tutorial
     - `Stable Diffusion Tutorial <https://github.com/NVIDIA/NeMo/blob/main/tutorials/multimodal/Stable%20Diffusion%20Tutorial.ipynb>`_
   * - Multimodal
     - Preparations and Advanced Applications: DreamBooth Tutorial
     - `DreamBooth Tutorial <https://github.com/NVIDIA/NeMo/blob/main/tutorials/multimodal/DreamBooth%20Tutorial.ipynb>`_
   * - Multimodal
     - Preparations and Advanced Applications: Stable Diffusion XL Quantization Tutorial
     - `SDXL Quantization Tutorial <https://github.com/NVIDIA/NeMo/blob/main/tutorials/multimodal/SDXL%20Quantization.ipynb>`_

.. list-table:: **Automatic Speech Recognition (ASR) Tutorials**
   :widths: 15 30 55
   :header-rows: 1

   * - Domain
     - Title
     - GitHub URL
   * - ASR
     - ASR with NeMo
     - `ASR with NeMo <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/ASR_with_NeMo.ipynb>`_
   * - ASR
     - ASR with Subword Tokenization
     - `ASR with Subword Tokenization <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/ASR_with_Subword_Tokenization.ipynb>`_
   * - ASR
     - Offline ASR
     - `Offline ASR <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/Offline_ASR.ipynb>`_
   * - ASR
     - Online ASR Microphone Cache Aware Streaming
     - `Online ASR Microphone Cache Aware Streaming <https://github.com/NVIDIA/NeMo/blob/stable/tutorials/asr/Online_ASR_Microphone_Demo_Cache_Aware_Streaming.ipynb>`_
   * - ASR
     - Online ASR Microphone Buffered Streaming
     - `Online ASR Microphone Buffered Streaming <https://github.com/NVIDIA/NeMo/blob/stable/tutorials/asr/Online_ASR_Microphone_Demo_Buffered_Streaming.ipynb>`_
   * - ASR
     - ASR CTC Language Fine-Tuning
     - `ASR CTC Language Fine-Tuning <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/ASR_CTC_Language_Finetuning.ipynb>`_
   * - ASR
     - Intro to Transducers
     - `Intro to Transducers <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/Intro_to_Transducers.ipynb>`_
   * - ASR
     - ASR with Transducers
     - `ASR with Transducers <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/ASR_with_Transducers.ipynb>`_
   * - ASR
     - ASR with Adapters
     - `ASR with Adapters <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/asr_adapters/ASR_with_Adapters.ipynb>`_
   * - ASR
     - Speech Commands
     - `Speech Commands <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/Speech_Commands.ipynb>`_
   * - ASR
     - Online Offline Microphone Speech Commands
     - `Online Offline Microphone Speech Commands <https://github.com/NVIDIA/NeMo/blob/stable/tutorials/asr/Online_Offline_Speech_Commands_Demo.ipynb>`_
   * - ASR
     - Voice Activity Detection
     - `Voice Activity Detection <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/Voice_Activity_Detection.ipynb>`_
   * - ASR
     - Online Offline Microphone VAD
     - `Online Offline Microphone VAD <https://github.com/NVIDIA/NeMo/blob/stable/tutorials/asr/Online_Offline_Microphone_VAD_Demo.ipynb>`_
   * - ASR
     - Speaker Recognition and Verification
     - `Speaker Recognition and Verification <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/speaker_tasks/Speaker_Identification_Verification.ipynb>`_
   * - ASR
     - Speaker Diarization Inference
     - `Speaker Diarization Inference <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/speaker_tasks/Speaker_Diarization_Inference.ipynb>`_
   * - ASR
     - ASR with Speaker Diarization
     - `ASR with Speaker Diarization <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/speaker_tasks/ASR_with_SpeakerDiarization.ipynb>`_
   * - ASR
     - Online Noise Augmentation
     - `Online Noise Augmentation <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/Online_Noise_Augmentation.ipynb>`_
   * - ASR
     - ASR for Telephony Speech
     - `ASR for Telephony Speech <https://github.com/NVIDIA/NeMo/blob/stable/tutorials/asr/ASR_for_telephony_speech.ipynb>`_
   * - ASR
     - Streaming inference
     - `Streaming inference <https://github.com/NVIDIA/NeMo/blob/stable/tutorials/asr/Streaming_ASR.ipynb>`_
   * - ASR
     - Buffered Transducer inference
     - `Buffered Transducer inference <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/Buffered_Transducer_Inference.ipynb>`_
   * - ASR
     - Buffered Transducer inference with LCS Merge
     - `Buffered Transducer inference with LCS Merge <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/Buffered_Transducer_Inference_with_LCS_Merge.ipynb>`_
   * - ASR
     - Offline ASR with VAD for CTC models
     - `Offline ASR with VAD for CTC models <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/Offline_ASR_with_VAD_for_CTC_models.ipynb>`_
   * - ASR
     - Self-supervised Pre-training for ASR
     - `Self-supervised Pre-training for ASR <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/Self_Supervised_Pre_Training.ipynb>`_
   * - ASR
     - Multi-lingual ASR
     - `Multi-lingual ASR <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/Multilang_ASR.ipynb>`_
   * - ASR
     - Hybrid ASR-TTS Models
     - `Hybrid ASR-TTS Models <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/ASR_TTS_Tutorial.ipynb>`_
   * - ASR
     - ASR Confidence Estimation
     - `ASR Confidence Estimation <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/ASR_Confidence_Estimation.ipynb>`_
   * - ASR
     - Confidence-based Ensembles
     - `Confidence-based Ensembles <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/asr/Confidence_Ensembles.ipynb>`_

.. list-table:: **Text-to-Speech (TTS) Tutorials**
   :widths: 15 35 50
   :header-rows: 1

   * - Domain
     - Title
     - GitHub URL
   * - TTS
     - Basic and Advanced: NeMo TTS Primer
     - `NeMo TTS Primer <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/tts/NeMo_TTS_Primer.ipynb>`_
   * - TTS
     - Basic and Advanced: TTS Speech/Text Aligner Inference
     - `TTS Speech/Text Aligner Inference <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/tts/Aligner_Inference_Examples.ipynb>`_
   * - TTS
     - Basic and Advanced: FastPitch and MixerTTS Model Training
     - `FastPitch and MixerTTS Model Training <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/tts/FastPitch_MixerTTS_Training.ipynb>`_
   * - TTS
     - Basic and Advanced: FastPitch Finetuning
     - `FastPitch Finetuning <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/tts/FastPitch_Finetuning.ipynb>`_
   * - TTS
     - Basic and Advanced: FastPitch and HiFiGAN Model Training for German
     - `FastPitch and HiFiGAN Model Training for German <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/tts/FastPitch_GermanTTS_Training.ipynb>`_
   * - TTS
     - Basic and Advanced: Tacotron2 Model Training
     - `Tacotron2 Model Training <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/tts/Tacotron2_Training.ipynb>`_
   * - TTS
     - Basic and Advanced: FastPitch Duration and Pitch Control
     - `FastPitch Duration and Pitch Control <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/tts/Inference_DurationPitchControl.ipynb>`_
   * - TTS
     - Basic and Advanced: FastPitch Speaker Interpolation
     - `FastPitch Speaker Interpolation <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/tts/FastPitch_Speaker_Interpolation.ipynb>`_
   * - TTS
     - Basic and Advanced: TTS Inference and Model Selection
     - `TTS Inference and Model Selection <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/tts/Inference_ModelSelect.ipynb>`_
   * - TTS
     - Basic and Advanced: TTS Pronunciation Customization
     - `TTS Pronunciation Customization <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/tts/Pronunciation_customization.ipynb>`_

.. list-table:: **Tools and Utilities**
   :widths: 15 25 60
   :header-rows: 1

   * - Domain
     - Title
     - GitHub URL
   * - Utility Tools
     - Utility Tools for Speech and Text: NeMo Forced Aligner
     - `NeMo Forced Aligner <https://colab.research.google.com/github/NVIDIA/NeMo/blob/main/tutorials/tools/NeMo_Forced_Aligner_Tutorial.ipynb>`_
   * - Utility Tools
     - Utility Tools for Speech and Text: Speech Data Explorer
     - `Speech Data Explorer <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/tools/SDE_HowTo_v2.ipynb>`_
   * - Utility Tools
     - Utility Tools for Speech and Text: CTC Segmentation
     - `CTC Segmentation <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/tools/CTC_Segmentation_Tutorial.ipynb>`_

.. list-table:: **Text Processing (TN/ITN) Tutorials**
   :widths: 25 35 60
   :header-rows: 1

   * - Domain
     - Title
     - GitHub URL
   * - Text Processing
     - Text Normalization Techniques: Text Normalization
     - `Text Normalization <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/text_processing/Text_(Inverse)_Normalization.ipynb>`_
   * - Text Processing
     - Text Normalization Techniques: Inverse Text Normalization with Thutmose Tagger
     - `Inverse Text Normalization with Thutmose Tagger <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/nlp/ITN_with_Thutmose_Tagger.ipynb>`_
   * - Text Processing
     - Text Normalization Techniques: WFST Tutorial
     - `WFST Tutorial <https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/text_processing/WFST_Tutorial.ipynb>`_
