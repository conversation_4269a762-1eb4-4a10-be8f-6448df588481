# Imagen
@inproceedings{saharia2022photorealistic,
      title={Photorealistic Text-to-Image Diffusion Models with Deep Language Understanding},
      author={<PERSON><PERSON><PERSON> and <PERSON> and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON> and <PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON> and <PERSON> and <PERSON> and <PERSON> and <PERSON>},
      booktitle={Conference on Neural Information Processing Systems (NeurIPS)},
      year={2022},
      doi={10.48550/arXiv.2205.11487}
}

# DDPM
@misc{ho2020denoising,
      title={Denoising Diffusion Probabilistic Models}, 
      author={<PERSON> and <PERSON><PERSON> and <PERSON>},
      year={2020},
      eprint={2006.11239},
      archivePrefix={arXiv},
      primaryClass={cs.LG}
}

# Continual DDPM
@misc{nichol2021improved,
      title={Improved Denoising Diffusion Probabilistic Models}, 
      author={<PERSON> and <PERSON><PERSON><PERSON><PERSON>},
      year={2021},
      eprint={2102.09672},
      archivePrefix={arXiv},
      primaryClass={cs.LG},
      url={https://arxiv.org/abs/2102.09672}, 
}

# EDM
@misc{karras2022elucidating,
      title={Elucidating the Design Space of Diffusion-Based Generative Models}, 
      author={Tero Karras and Miika Aittala and Timo Aila and Samuli Laine},
      year={2022},
      eprint={2206.00364},
      archivePrefix={arXiv},
      primaryClass={cs.CV}
}

# Make-A-Scene
@misc{gafni2022makeascene,
      title={Make-A-Scene: Scene-Based Text-to-Image Generation with Human Priors},
      author={Oran Gafni and Adam Polyak and Oron Ashual and Shelly Sheynin and Devi Parikh and Yaniv Taigman},
      year={2022},
      eprint={2203.13131},
      archivePrefix={arXiv},
      primaryClass={cs.CV}
}

# Stable Diffusion
@misc{rombach2022highresolution,
      title={High-Resolution Image Synthesis with Latent Diffusion Models},
      author={Robin Rombach and Andreas Blattmann and Dominik Lorenz and Patrick Esser and Björn Ommer},
      year={2022},
      eprint={2112.10752},
      archivePrefix={arXiv},
      primaryClass={cs.CV}
}

# Parti
@misc{yu2022scaling,
      title={Scaling Autoregressive Models for Content-Rich Text-to-Image Generation},
      author={Jiahui Yu and Yuanzhong Xu and Jing Yu Koh and Thang Luong and Gunjan Baid and Zirui Wang and Vijay Vasudevan and Alexander Ku and Yinfei Yang and Burcu Karagol Ayan and Ben Hutchinson and Wei Han and Zarana Parekh and Xin Li and Han Zhang and Jason Baldridge and Yonghui Wu},
      year={2022},
      eprint={2206.10789},
      archivePrefix={arXiv},
      primaryClass={cs.CV}
}

# MUSE
@misc{chang2023muse,
      title={Muse: Text-To-Image Generation via Masked Generative Transformers},
      author={Huiwen Chang and Han Zhang and Jarred Barber and AJ Maschinot and Jose Lezama and Lu Jiang and Ming-Hsuan Yang and Kevin Murphy and William T. Freeman and Michael Rubinstein and Yuanzhen Li and Dilip Krishnan},
      year={2023},
      eprint={2301.00704},
      archivePrefix={arXiv},
      primaryClass={cs.CV}
}

# Ins P2P
@misc{insp2p2022,
      Author = {Tim Brooks and Aleksander Holynski and Alexei A. Efros},
      Title = {InstructPix2Pix: Learning to Follow Image Editing Instructions},
      Year = {2022},
      Eprint = {arXiv:2211.09800},
}

# Dream Booth
@misc{ruiz2023dreambooth,
      title={DreamBooth: Fine Tuning Text-to-Image Diffusion Models for Subject-Driven Generation},
      author={Nataniel Ruiz and Yuanzhen Li and Varun Jampani and Yael Pritch and Michael Rubinstein and Kfir Aberman},
      year={2023},
      eprint={2208.12242},
      archivePrefix={arXiv},
      primaryClass={cs.CV}
}

# Control Net
@misc{zhang2023adding,
      title={Adding Conditional Control to Text-to-Image Diffusion Models},
      author={Lvmin Zhang and Anyi Rao and Maneesh Agrawala},
      year={2023},
      eprint={2302.05543},
      archivePrefix={arXiv},
      primaryClass={cs.CV}
}

# LLAva
@misc{llava,
      Author = {Haotian Liu and Chunyuan Li and Qingyang Wu and Yong Jae Lee},
      Title = {Visual Instruction Tuning},
      Year = {2023},
      Eprint = {arXiv:2304.08485},
}

@misc{liu2023improvedllava,
      title={Improved Baselines with Visual Instruction Tuning},
      author={Liu, Haotian and Li, Chunyuan and Li, Yuheng and Lee, Yong Jae},
      publisher={arXiv:2310.03744},
      year={2023},
}

@misc{minigpt4,
      Author = {Deyao Zhu and Jun Chen and Xiaoqian Shen and Xiang Li and Mohamed Elhoseiny},
      Title = {MiniGPT-4: Enhancing Vision-Language Understanding with Advanced Large Language Models},
      Year = {2023},
      Eprint = {arXiv:2304.10592},
}

@misc{flamingo,
      Author = {Jean-Baptiste Alayrac and Jeff Donahue and Pauline Luc and Antoine Miech and Iain Barr and Yana Hasson and Karel Lenc and Arthur Mensch and Katie Millican and Malcolm Reynolds and Roman Ring and Eliza Rutherford and Serkan Cabi and Tengda Han and Zhitao Gong and Sina Samangooei and Marianne Monteiro and Jacob Menick and Sebastian Borgeaud and Andrew Brock and Aida Nematzadeh and Sahand Sharifzadeh and Mikolaj Binkowski and Ricardo Barreira and Oriol Vinyals and Andrew Zisserman and Karen Simonyan},
      Title = {Flamingo: a Visual Language Model for Few-Shot Learning},
      Year = {2022},
      Eprint = {arXiv:2204.14198},
}

@misc{blip2,
      Author = {Junnan Li and Dongxu Li and Silvio Savarese and Steven Hoi},
      Title = {BLIP-2: Bootstrapping Language-Image Pre-training with Frozen Image Encoders and Large Language Models},
      Year = {2023},
      Eprint = {arXiv:2301.12597},
}

@misc{kosmos1,
      Author = {Shaohan Huang and Li Dong and Wenhui Wang and Yaru Hao and Saksham Singhal and Shuming Ma and Tengchao Lv and Lei Cui and Owais Khan Mohammed and Barun Patra and Qiang Liu and Kriti Aggarwal and Zewen Chi and Johan Bjorck and Vishrav Chaudhary and Subhojit Som and Xia Song and Furu Wei},
      Title = {Language Is Not All You Need: Aligning Perception with Language Models},
      Year = {2023},
      Eprint = {arXiv:2302.14045},
}

# DECLIP
@misc{li2021declip,
      title={Supervision Exists Everywhere: A Data Efficient Contrastive Language-Image Pre-training Paradigm},
      author={Yangguang Li and Feng Liang and Lichen Zhao and Yufeng Cui and Wanli Ouyang and Jing Shao and Fengwei Yu and Junjie Yan},
      year={2021},
      eprint={2110.05208},
      archivePrefix={arXiv},
      primaryClass={cs.CV},
      url={https://ar5iv.org/abs/2110.05208}
}

# CLIP
@misc{radford2021learning,
      title={Learning Transferable Visual Models From Natural Language Supervision},
      author={Alec Radford and Jong Wook Kim and Chris Hallacy and Aditya Ramesh and Gabriel Goh and Sandhini Agarwal and Girish Sastry and Amanda Askell and Pamela Mishkin and Jack Clark and Gretchen Krueger and Ilya Sutskever},
      year={2021},
      eprint={2103.00020},
      archivePrefix={arXiv},
      primaryClass={cs.CV}
}

# FLAVA
@inproceedings{singh2022flava,
      title={FLAVA: A Foundational Language And Vision Alignment Model},
      author={Amanpreet Singh and Ronghang Hu and Vedanuj Goswami and Guillaume Couairon and Wojciech Galuba and Marcus Rohrbach and Douwe Kiela},
      booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
      pages={15638--15650},
      year={2022}
}

# ControlNet GITHUB
@misc{controlnetgithub,
      title={Lllyasviel/controlnet},
      url={https://github.com/lllyasviel/ControlNet},
      journal={GitHub},
      author={Lllyasviel, Zhang},
      year={2023}
}

#DreamBooth Github
@misc{dreamboothdataset,
      title={DreamBooth},
      url={https://github.com/google/dreambooth/tree/main/dataset},
      journal={GitHub},
      author={Google},
      year={2023}
}

#DreamBooth Paper
@misc{dreamboothpaper,
      title={DreamBooth: Fine Tuning Text-to-Image Diffusion Models
for Subject-Driven Generation},
      url={https://arxiv.org/abs/2208.12242},
      author={Nataniel Ruiz and Yuanzhen Li and Varun Jampani and Yael Pritch and Michael Rubinstein and Kfir Aberman},
      year={2022},
      archivePrefix={arXiv}
}

# DreamFusion paper
@misc{poole2022dreamfusion,
      title={DreamFusion: Text-to-3D using 2D Diffusion},
      url={https://arxiv.org/abs/2209.14988},
      author={Poole, Ben and Jain, Ajay and Barron, Jonathan T. and Mildenhall, Ben},
      year={2022},
      archivePrefix={arXiv},
}