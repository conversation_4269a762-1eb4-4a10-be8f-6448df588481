@import url("theme.css");

body {
	font-size: 100%;
	font-family: 'NVIDIA Sans', sans-serif;
}


/* Width of template */

.wy-nav-content {
	max-width: 1200px !important;
}



/* Standard Text Formatting */

h1 {
	color: #76b900;
	text-align: center;
	/* background-color: #ffffff; */
}

h2 {
	color: #ffffff;
	/* background-color: #ffffff; */
	/* #76b900 */
	Padding: 5px;
}

h3 {
	padding-top: 0px;
	border-top: solid 3px #000000;
	/* #76b900 */
	border-bottom: solid 3px #000000;
	/* #76b900 */
}

p {
	margin-bottom: 24px;
}

/* Link Colors */
/*
a {
    color: #76b900;
}
/*

/*
a:visited {
	color: #218219;
}
*/

.container-xl {
	margin-right: unset;
	margin-left: unset;
}

section {
	overflow-x: auto;
}

/* ----------------------------------------------TABLES--------------------------------------- */
section table {
	overflow-x: auto;
	display: block;
}

table {
	font-size: small;
}

/* Table head Color */
thead td {
	background-color: #333333 !important;
}

.row-odd p {
	/*padding-bottom: 0px;*/
	/*margin-bottom: 0px;*/
}

/* even rows*/

.row-even tr {
	background-color: #e5f1e6 !important;
}

/* odd rows*/


.wy-table-responsive table tr {
	background-color: #ffffff !important;
}



.wy-table-responsive table td {
	white-space: normal;
}


/* Removes bottom margin in tables*/

.rst-content .line-block {
	margin-bottom: 0px;
}

.wy-table-responsive {
	overflow: visible !important;
}

/* reduces the size of text in multiline table columns. */

.rst-content table.docutils td {
	font-size: 80%;
}

.rst-content dl:not(.docutils) dt {

	background-color: inherit;
	color: #000000;
	border-top: solid 0px #000000;

}

.rst-content dl:not(.docutils) dt:before {
	color: #333333;
}

.rst-content .line-block {
	margin-bottom: 0px;
}

.wy-side-nav-search,
.wy-nav-top {
	background-color: #000000;
	padding: 0;
}

.wy-side-nav-search img {
	padding: 0px;
	padding: 0px 0px;
	margin-bottom: 0;
}

.wy-side-nav-search input[type=text] {
	border-radius: 0px;
}


.wy-menu-vertical p.caption {
	color: #76b900;
}


.wy-side-nav-search>a img.logo,
.wy-side-nav-search .wy-dropdown>a img.logo {
	margin: 0px 0px 0px 0px;
}

.wy-nav-content {
	margin: 0;
	min-height: 100%;
	height: 100%;
	background: #ffffff;
}

/* List (numbered, bulleted) padding Fix */


.wy-plain-list-decimal li {
	margin-top: -6px;
	margin-bottom: -6px;
}

.rst-content .section ol.loweralpha {
	margin-top: -6px;
	margin-bottom: 12px;
}

.wy-plain-list-disc,
.rst-content .toctree-wrapper ul,
article ul {
	margin-top: 0px !important;
	margin-bottom: 12px;
}

/* Alert Boxes */
/* Background color of Alert Box Title */

.rst-content .section ul {
	margin-top: -12px;
	margin-bottom: 16px;
}

.wy-alert.wy-alert-info .wy-alert-title,
.rst-content .note .wy-alert-title,
.rst-content .wy-alert-info.attention .wy-alert-title,
.rst-content .wy-alert-info.caution .wy-alert-title,
.rst-content .wy-alert-info.danger .wy-alert-title,
.rst-content .wy-alert-info.error .wy-alert-title,
.rst-content .wy-alert-info.hint .wy-alert-title,
.rst-content .wy-alert-info.important .wy-alert-title,
.rst-content .wy-alert-info.tip .wy-alert-title,
.rst-content .wy-alert-info.warning .wy-alert-title,
.rst-content .seealso .wy-alert-title,
.rst-content .wy-alert-info.admonition-todo .wy-alert-title,
.rst-content .wy-alert-info.admonition .wy-alert-title,
.wy-alert.wy-alert-info .rst-content .admonition-title,
.rst-content .wy-alert.wy-alert-info .admonition-title,
.rst-content .note .admonition-title,
.rst-content .wy-alert-info.attention .admonition-title,
.rst-content .wy-alert-info.caution .admonition-title,
.rst-content .wy-alert-info.danger .admonition-title,
.rst-content .wy-alert-info.error .admonition-title,
.rst-content .wy-alert-info.hint .admonition-title,
.rst-content .wy-alert-info.important .admonition-title,
.rst-content .wy-alert-info.tip .admonition-title,
.rst-content .wy-alert-info.warning .admonition-title,
.rst-content .seealso .admonition-title,
.rst-content .wy-alert-info.admonition-todo .admonition-title,
.rst-content .wy-alert-info.admonition .admonition-title {
	background: #76b900;
}

/* Background and Font Color of Alert Box Main Body*/
.wy-alert.wy-alert-info,
.rst-content .note,
.rst-content .wy-alert-info.attention,
.rst-content .wy-alert-info.caution,
.rst-content .wy-alert-info.danger,
.rst-content .wy-alert-info.error,
.rst-content .wy-alert-info.hint,
.rst-content .wy-alert-info.important,
.rst-content .wy-alert-info.tip,
.rst-content .wy-alert-info.warning,
.rst-content .seealso,
.rst-content .wy-alert-info.admonition-todo,
.rst-content .wy-alert-info.admonition {
	background: #333333;
	color: #999999;
}

.section {
	margin-top: 50px;
}

/* Logo */
.navbar-brand-box {
	background-color: #ffffff;
}

/* ---------------------------------------------- Media Queries --------------------------------------- */
@media (min-width: 1200px) {
	.container-xl {
		max-width: 100%;
	}
}

@media (min-width: none) {
	body {
		font-size: 18px;
	}

	#site-navigation nav ul.nav {
		font-size: 18px;
	}

	#site-navigation nav.bd-links p {
		font-size: 18px;
	}

	#site-navigation {
		width: 350px;
	}

	.toc-h2 {
		font-size: 18px;
	}

	.toc-h3 {
		font-size: 1rem;
	}

	.toc-h4 {
		font-size: 0.85rem;
	}

	.header-article .bd-toc {
		font-size: 18px;
	}

	#main-content>div {
		margin-left: 10%;
		margin-right: 10%;
	}
}

/* ---------------------------------------------- NVIDIA Sans --------------------------------------- */

:root {
	--md-text-font: "NVIDIA Sans";
	/* --md-code-font: "NVIDIA Sans"; */
}

@font-face {
	font-family: "NVIDIA Sans";
	src: url(https://aws1.discourse-cdn.com/nvidia/original/3X/5/2/52891dda673228d54e5d57bf1e4a3880d4b22405.woff2) format("woff2"),
		url(https://aws1.discourse-cdn.com/nvidia/original/3X/e/0/e090b7dda7a582522c7f9045c6ce949cce60134f.woff) format("woff");
	font-weight: 300;
	font-style: normal;
}

@font-face {
	font-family: "NVIDIA Sans";
	src: url(https://aws1.discourse-cdn.com/nvidia/original/3X/a/1/a107baabcbf6b241099122336bce7429bcfd377a.woff2) format("woff2"),
		url(https://aws1.discourse-cdn.com/nvidia/original/3X/3/a/3a6060a4e3bce70e5552ba0de8af4b22c6cf9144.woff) format("woff");
	font-weight: 300;
	font-style: italic;
}

@font-face {
	font-family: "NVIDIA Sans";
	src: url(https://aws1.discourse-cdn.com/nvidia/original/3X/9/9/9920d2b172b01d92fc9c1c0e521dcf45b59c47c3.woff2) format("woff2"),
		url(https://aws1.discourse-cdn.com/nvidia/original/3X/6/c/6c7d947928a7e4ef3e80ed409bef6c243f2148cb.woff) format("woff");
	font-weight: 400;
	font-style: normal;
}

@font-face {
	font-family: "NVIDIA Sans";
	src: url(https://aws1.discourse-cdn.com/nvidia/original/3X/e/8/e8e63fe1244372cd942d957f44a5616a1eba0644.woff2) format("woff2"),
		url(https://aws1.discourse-cdn.com/nvidia/original/3X/0/f/0f1fb2af0283ab09d36e7097bb07d895c3228f12.woff) format("woff");
	font-weight: 400;
	font-style: italic;
}

@font-face {
	font-family: "NVIDIA Sans";
	src: url(https://aws1.discourse-cdn.com/nvidia/original/3X/7/9/79d3c513a9cd72c59f65354f39f89ca52dc17dd2.woff2) format("woff2"),
		url(https://aws1.discourse-cdn.com/nvidia/original/3X/2/5/2581ac533f5d01f4985d8a7245b0766b4630ced8.woff) format("woff");
	font-weight: 500;
	font-style: normal;
}

@font-face {
	font-family: "NVIDIA Sans";
	src: url(https://aws1.discourse-cdn.com/nvidia/original/3X/3/9/39d9ef1ee9770dd503f19bb2ace2fdb4eff3bb50.woff2) format("woff2"),
		url(https://aws1.discourse-cdn.com/nvidia/original/3X/7/b/7bb5d5e2e71b2e13c8098b2e67c0a0ed9258e6c7.woff) format("woff");
	font-weight: 500;
	font-style: italic;
}

@font-face {
	font-family: "NVIDIA Sans";
	src: url(https://aws1.discourse-cdn.com/nvidia/original/3X/0/5/05276a55a43eb3f74981ec1e93252727afcd9d16.woff2) format("woff2"),
		url(https://aws1.discourse-cdn.com/nvidia/original/3X/9/c/9cfec7ed941b06564aa4d5ca14610e81542d070f.woff) format("woff");
	font-weight: 700;
	font-style: normal;
}

@font-face {
	font-family: "NVIDIA Sans";
	src: url(https://aws1.discourse-cdn.com/nvidia/original/3X/a/e/aebd14d09ba56f541e1b8735fb051e33710f9ae7.woff2) format("woff2"),
		url(https://aws1.discourse-cdn.com/nvidia/original/3X/e/d/edbdabef43acc5c12e84a94baaa5542c9404cfeb.woff) format("woff");
	font-weight: 700;
	font-style: italic;
}