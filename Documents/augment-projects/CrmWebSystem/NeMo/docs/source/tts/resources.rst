Resources and Documentation
===========================

Hands-on TTS tutorial notebooks can be found under `the TTS tutorials folder <https://github.com/NVIDIA/NeMo/tree/stable/tutorials/tts/>`_. If you are a beginner to NeMo, consider trying out the tutorials of `NeMo Primer <https://github.com/NVIDIA/NeMo/tree/stable/tutorials/00_NeMo_Primer.ipynb>`_ and `NeMo Model <https://github.com/NVIDIA/NeMo/tree/stable/tutorials/01_NeMo_Models.ipynb>`_. If you are also a beginner to TTS, consider trying out the `NeMo TTS Primer Tutorial <https://github.com/NVIDIA/NeMo/tree/stable/tutorials/tts/NeMo_TTS_Primer.ipynb>`_. These tutorials can be run on Google Colab by specifying the link to the notebooks' GitHub pages on Colab.

If you are looking for information about a particular TTS model, or would like to find out more about the model architectures available in the directory of `nemo.collections.tts <https://github.com/NVIDIA/NeMo/tree/stable/nemo/collections/tts/models>`_, refer to the :doc:`Models <./models>` section.

NeMo includes preprocessing scripts for several common TTS datasets. The :doc:`Data Preprocessing <./datasets>` section contains instructions on how to run those scripts. You can also creating your own NeMo-compatible dataset preprocessing script by following the guidance.

Information about how to load model checkpoints (either local files or pretrained ones from NGC), as well as a list of the checkpoints available on NGC are located on the :doc:`Checkpoints <./checkpoints>` section.

Documentation regarding the configuration files specific to the NeMo TTS models can be found on the :doc:`Configuration Files <./configs>` section.
