Model Name,Dataset,Sampling Rate,Model Class,Overview,Checkpoint
audio_codec_16khz_small,Libri-Light,16000Hz,nemo.collections.tts.models.AudioCodecModel,`audio_codec_16khz_small <https://ngc.nvidia.com/catalog/models/nvidia:nemo:audio_codec_16khz_small>`_,``https://api.ngc.nvidia.com/v2/models/nvidia/nemo/audio_codec_16khz_small/versions/v1/files/audio_codec_16khz_small.nemo``
mel_codec_22khz_medium,LibriVox and Common Voice,22050Hz,nemo.collections.tts.models.AudioCodecModel,`mel_codec_22khz_medium <https://ngc.nvidia.com/catalog/models/nvidia:nemo:mel_codec_22khz_medium>`_,``https://api.ngc.nvidia.com/v2/models/nvidia/nemo/mel_codec_22khz_medium/versions/v1/files/mel_codec_22khz_medium.nemo``
mel_codec_44khz_medium,LibriVox and Common Voice,44100Hz,nemo.collections.tts.models.AudioCodecModel,`mel_codec_44khz_medium <https://ngc.nvidia.com/catalog/models/nvidia:nemo:mel_codec_44khz_medium>`_,``https://api.ngc.nvidia.com/v2/models/nvidia/nemo/mel_codec_44khz_medium/versions/v1/files/mel_codec_44khz_medium.nemo``
mel_codec_22khz_fullband_medium,LibriVox and Common Voice,22050Hz,nemo.collections.tts.models.AudioCodecModel,`mel_codec_22khz_fullband_medium <https://ngc.nvidia.com/catalog/models/nvidia:nemo:mel_codec_22khz_fullband_medium>`_,``https://api.ngc.nvidia.com/v2/models/nvidia/nemo/mel_codec_22khz_fullband_medium/versions/v1/files/mel_codec_22khz_fullband_medium.nemo``
mel_codec_44khz_fullband_medium,LibriVox and Common Voice,44100Hz,nemo.collections.tts.models.AudioCodecModel,`mel_codec_44khz_fullband_medium <https://ngc.nvidia.com/catalog/models/nvidia:nemo:mel_codec_44khz_fullband_medium>`_,``https://api.ngc.nvidia.com/v2/models/nvidia/nemo/mel_codec_44khz_fullband_medium/versions/v1/files/mel_codec_44khz_fullband_medium.nemo``
nvidia/low-frame-rate-speech-codec-22khz,LibriVox and Common Voice,22050Hz,nemo.collections.tts.models.AudioCodecModel,`audio_codec_low_frame_rate_22khz <https://huggingface.co/nvidia/low-frame-rate-speech-codec-22khz>`_,``https://huggingface.co/nvidia/low-frame-rate-speech-codec-22khz/resolve/main/low-frame-rate-speech-codec-22khz.nemo``
nvidia/audio-codec-22khz,LibriVox and Common Voice,22050Hz,nemo.collections.tts.models.AudioCodecModel,`audio-codec-22khz <https://huggingface.co/nvidia/audio-codec-22khz>`_,``https://huggingface.co/nvidia/audio-codec-22khz/resolve/main/audio-codec-22khz.nemo``
nvidia/audio-codec-44khz,LibriVox and Common Voice,44100Hz,nemo.collections.tts.models.AudioCodecModel,`audio-codec-44khz <https://huggingface.co/nvidia/audio-codec-44khz>`_,``https://huggingface.co/nvidia/audio-codec-44khz/resolve/main/audio-codec-44khz.nemo``
nvidia/mel-codec-22khz,LibriVox and Common Voice,22050Hz,nemo.collections.tts.models.AudioCodecModel,`mel-codec-22khz <https://huggingface.co/nvidia/mel-codec-22khz>`_,``https://huggingface.co/nvidia/mel-codec-22khz/resolve/main/mel-codec-22khz.nemo``
nvidia/mel-codec-44khz,LibriVox and Common Voice,44100Hz,nemo.collections.tts.models.AudioCodecModel,`mel-codec-44khz <https://huggingface.co/nvidia/mel-codec-44khz>`_,``https://huggingface.co/nvidia/mel-codec-44khz/resolve/main/mel-codec-44khz.nemo``