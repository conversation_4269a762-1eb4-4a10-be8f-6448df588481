Speech Data Processor
=====================

Speech Data Processor (SDP) is a toolkit to make it easy to:
  1. write code to process a new dataset, minimizing the amount of boilerplate code required.
  2. share the steps for processing a speech dataset.

SDP is hosted here: https://github.com/NVIDIA/NeMo-speech-data-processor.

To learn more about SDP, please check the [documentation](https://nvidia.github.io/NeMo-speech-data-processor/).
