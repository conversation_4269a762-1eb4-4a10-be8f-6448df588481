Resources and Documentation
===========================

Tutorial notebooks can be found under `the audio tutorials folder <https://github.com/NVIDIA/NeMo/tree/main/tutorials/audio>`_. If you are just starting with NeMo, consider trying out the tutorials of `NeMo Primer <https://github.com/NVIDIA/NeMo/blob/main/tutorials/00_NeMo_Primer.ipynb>`_ and `NeMo Model <https://github.com/NVIDIA/NeMo/blob/main/tutorials/01_NeMo_Models.ipynb>`_. These tutorials can be run on Google Colab by specifying the link to the notebooks' GitHub pages on Colab.

If you are looking for information about a particular model, or would like to find out more about the model architectures available in the directory of `nemo.collections.audio <https://github.com/NVIDIA/NeMo/tree/main/nemo/collections/audio>`_, refer to the :doc:`Models <./models>` section.

Information about how to load model checkpoints (either local files or pretrained ones from NGC), as well as a list of the checkpoints available on NGC are located on the :doc:`Checkpoints <./checkpoints>` section.

Documentation regarding the configuration files specific to the NeMo audio models can be found on the :doc:`Configuration Files <./configs>` section.
