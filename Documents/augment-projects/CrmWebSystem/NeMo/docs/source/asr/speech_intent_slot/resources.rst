Resources and Documentation
---------------------------

The example code for Speech Intent Classification and Slot Filling can be found `here <https://github.com/NVIDIA/NeMo/tree/stable/examples/slu/speech_intent_slot/README.md>`_.

Information about how to load model checkpoints (either local files or pretrained ones from NGC), 
as well as a list of the checkpoints available on NGC are located on the :doc:`Checkpoints <./results>` 
page.

Documentation regarding the configuration files specific to SLU can be found in the 
:doc:`Configuration Files <./configs>` page.
