..
  AUTOGENERATED DOC: DO NOT EDIT MANUALLY !

Scores
------

E<PERSON>
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/en/citrinet_en.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/en/conformer_en.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/en/contextnet_en.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/en/fastconformer_en.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/en/jasper10x5dr_en.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/en/quartznet15x5_en.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/en/squeezeformer_en.csv

--------------------

BE
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/be/conformer_be.csv

--------------------

BY
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/by/fastconformer_by.csv

--------------------

CA
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/ca/conformer_ca.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/ca/quartznet15x5_ca.csv

--------------------

DE
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/de/citrinet_de.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/de/conformer_de.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/de/contextnet_de.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/de/fastconformer_de.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/de/quartznet15x5_de.csv

--------------------

ENES
^^^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/enes/conformer_enes.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/enes/contextnet_enes.csv

--------------------

EO
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/eo/conformer_eo.csv

--------------------

ES
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/es/citrinet_es.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/es/conformer_es.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/es/contextnet_es.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/es/fastconformer_es.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/es/quartznet15x5_es.csv

--------------------

FR
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/fr/citrinet_fr.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/fr/conformer_fr.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/fr/contextnet_fr.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/fr/quartznet15x5_fr.csv

--------------------

HR
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/hr/conformer_hr.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/hr/fastconformer_hr.csv

--------------------

IT
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/it/conformer_it.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/it/fastconformer_it.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/it/quartznet15x5_it.csv

--------------------

KAB
^^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/kab/conformer_kab.csv

--------------------

NL
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/nl/fastconformer_nl.csv

--------------------

PL
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/pl/fastconformer_pl.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/pl/quartznet15x5_pl.csv

--------------------

RU
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/ru/conformer_ru.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/ru/quartznet15x5_ru.csv

--------------------

RW
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/rw/conformer_rw.csv

--------------------

UA
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/ua/fastconformer_ua.csv

--------------------

ZH
^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/zh/citrinet_zh.csv

--------------------

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores/zh/conformer_zh.csv

--------------------


Scores with Punctuation and Capitalization
------------------------------------------

EN with P&C
^^^^^^^^^^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores_pc/en/fastconformer_en.csv

--------------------

BY with P&C
^^^^^^^^^^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores_pc/by/fastconformer_by.csv

--------------------

DE with P&C
^^^^^^^^^^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores_pc/de/fastconformer_de.csv

--------------------

ES with P&C
^^^^^^^^^^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores_pc/es/fastconformer_es.csv

--------------------

HR with P&C
^^^^^^^^^^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores_pc/hr/fastconformer_hr.csv

--------------------

IT with P&C
^^^^^^^^^^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores_pc/it/fastconformer_it.csv

--------------------

NL with P&C
^^^^^^^^^^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores_pc/nl/fastconformer_nl.csv

--------------------

PL with P&C
^^^^^^^^^^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores_pc/pl/fastconformer_pl.csv

--------------------

UA with P&C
^^^^^^^^^^^

.. csv-table::
    :header-rows: 1
    :align: left
    :file: data/scores_pc/ua/fastconformer_ua.csv
