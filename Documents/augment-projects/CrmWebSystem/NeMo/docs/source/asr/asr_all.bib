@article{matchboxnet,
  title={{MatchboxNet}: 1D Time-Channel Separable Convolutional Neural Network Architecture for Speech Commands Recognition},
  author={<PERSON><PERSON><PERSON>, Somshubra and Ginsburg, Boris},
  journal={Proc. Interspeech 2020},
  year={2020}
}

@article{marblenet,
  title={MarbleNet: Deep 1D Time-Channel Separable Convolutional Neural Network for Voice Activity Detection},
  author={<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, Somshubra and Ginsburg, Boris},
  journal={arXiv preprint arXiv:2010.13886},
  year={2020}
}

@inproceedings{panayotov2015librispeech,
  title={Librispeech: an ASR corpus based on public domain audio books},
  author={Panayotov, V<PERSON>il and <PERSON>, Guoguo and Povey, Daniel and Khudanpur, Sanjeev},
  booktitle={Acoustics, Speech and Signal Processing (ICASSP), 2015 IEEE International Conference on},
  pages={5206--5210},
  year={2015},
  organization={IEEE}
}

@article{luong17,
  author  = {Minh{-}<PERSON><PERSON> and <PERSON> and <PERSON><PERSON>},
  title   = {Neural Machine Translation (seq2seq) Tutorial},
  journal = {https://github.com/tensorflow/nmt},
  year    = {2017},
}

@INPROCEEDINGS{LaurentSeqWiseBN,
author={C. {Laurent} and G. {Pereyra} and P. {Brakel} and Y. {Zhang} and Y. {Bengio}},
booktitle={2016 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)},
title={Batch normalized recurrent neural networks},
year={2016},
volume={},
number={},
pages={2657-2661},
keywords={feedforward neural nets;learning (artificial intelligence);recurrent neural nets;speech recognition;batch normalized recurrent neural networks;RNN;sequential data;long-term dependency learning;convergence rate improvement;intermediate representation normalization;feedforward neural networks;speech recognition task;language modeling;training criterion;Training;Recurrent neural networks;Convergence;Speech recognition;Computer architecture;Speech;batch normalization;RNN;LSTM;optimization},
doi={10.1109/ICASSP.2016.7472159},
ISSN={2379-190X},
month={March},}

@article{graves2005,
  author  = {Alex Graves and Jurgen Schmidhuber},
  title   = {Framewise phoneme classification with bidirectional LSTM and other neural network architectures},
  journal = {Neural Networks, vol. 18},
  pages={602–-610},
  year    = {2005},
}

@inproceedings{graves2006,
  title={Connectionist temporal classification: labelling unsegmented sequence data with recurrent neural networks},
  author={Graves, Alex and Fern{\'a}ndez, Santiago and Gomez, Faustino and Schmidhuber, J{\"u}rgen},
  booktitle={Proceedings of the 23rd international conference on Machine learning},
  pages={369--376},
  year={2006},
  organization={ACM}
}

@article{li2019jasper,
  title={Jasper: An End-to-End Convolutional Neural Acoustic Model},
  author={Li, Jason and Lavrukhin, Vitaly and Ginsburg, Boris and Leary, Ryan and Kuchaiev, Oleksii and Cohen, Jonathan M and Nguyen, Huyen and Gadde, Ravi Teja},
  journal={arXiv preprint arXiv:1904.03288},
  year={2019}
}

@misc{ardila2019common,
    title={Common Voice: A Massively-Multilingual Speech Corpus},
    author={Rosana Ardila and Megan Branson and Kelly Davis and Michael Henretty and Michael Kohler and Josh Meyer and Reuben Morais and Lindsay Saunders and Francis M. Tyers and Gregor Weber},
    year={2019},
    eprint={1912.06670},
    archivePrefix={arXiv},
    primaryClass={cs.CL}
}

@article{graves2012,
  title={Sequence Transduction with Recurrent Neural Networks},
  author={Graves, Alex},
  journal={arXiv preprint arXiv:1211.3711},
  year={2012}
}


@article{graves2013,
  title={Generating sequences with recurrent neural networks},
  author={Graves, Alex},
  journal={arXiv preprint arXiv:1308.0850},
  year={2013}
}

@article{sergeev2018horovod,
  title={Horovod: fast and easy distributed deep learning in TensorFlow},
  author={Sergeev, Alexander and Del Balso, Mike},
  journal={arXiv preprint arXiv:1802.05799},
  year={2018}
}

@misc{NVVolta,
  title = {NVIDIA TESLA V100 GPU ARCHITECTURE},
  howpublished = {\url{http://images.nvidia.com/content/volta-architecture/pdf/volta-architecture-whitepaper.pdf}},
  note = {Accessed: 2018-10-09}
}

@article{NVTuring,
  title = {NVIDIA TURING GPU ARCHITECTURE},
  howpublished = {\url{https://www.nvidia.com/content/dam/en-zz/Solutions/design-visualization/technologies/turing-architecture/NVIDIA-Turing-Architecture-Whitepaper.pdf}},
  author = {NVIDIA},
  year = {2018},
  note = {Accessed: 2018-10-09}
}

@misc{Rygaard2015,
  title = {Using Synthesized Speech to Improve Speech Recognition for Low-Resource Languages},
  author = {Luise Valentin Rygaard},
  howpublished = {\url{https://parasol.tamu.edu/dreu2015/Rygaard/report.pdf}},
  year = {2015},
}

@misc{OpenSeq2Seq,
  title = {OpenSeq2Seq: extensible toolkit for distributed and mixed precision training of sequence-to-sequence models},
  author = {Kuchaiev, Oleksii and Ginsburg, Boris and Gitman, Igor and  Lavrukhin,Vitaly and   Case, Carl and   Micikevicius, Paulius},
  howpublished = {\url{https://arxiv.org/abs/1805.10387}},
  year = {2018},
}

@misc{MPGuide,
  title = {Training with Mixed Precision},
  howpublished = {\url{http://docs.nvidia.com/deeplearning/sdk/mixed-precision-training/}},
  note = {Accessed: 2018-04-06},
}

@misc{Mozilla,
  title = {Mozilla: A Journey to less than 10\% Word Error Rate},
  howpublished = {\url{https://hacks.mozilla.org/2017/11/a-journey-to-10-word-error-rate/}},
  note = {Accessed: 2018-04-06},
}

@article{Waibel1989,
  title={A time-delay neural network architecture for isolated word recognition},
  author={Waibel, Alexander,  and Hanazawa, Toshiyki and Hinton,Geoffrey and Shirano, Kiyohiro and  Lang, Kevin },
  journal={IEEE Trans. on Acoustics, Speech and Signal Processing},
  year={1989}
}

@article{Lang1990,
  title={A time-delay neural network architecture for isolated word recognition},
  author={Lang, Kevin and Waibel, Alexander, and Hinton,Geoffrey },
  journal={Neural Networks},
  year={1990}
}

@book{Bengio1996,
	Author = {Bengio, Y.},
	Publisher = {International Thomson Computer Press},
	Title = {Neural Networks for Speech and Sequence Recognition},
	Year = {1996}
}

@article{Bengio1992,
  title={Global optimization of a neural network-hidden Markov model hybrid},
  author={Bengio, Y., and De Mori, R., and Flammia, G., and Kompe, R. },
  journal={IEEE Transactions on Neural Networks, 3(2), 252–259},
  year={1992}
}

@article{Bourlard1994,
  title={Connectionist speech recognition: a hybrid approach},
  author={Bourlard, H. A. and Morgan, N.},
  journal={volume 247 Springer },
  year={1994}
}

@article{srivastava14a,
  author  = {Nitish Srivastava, and Geoffrey Hinton, and Alex Krizhevsky, and Ilya Sutskever, and Ruslan Salakhutdinov},
  title   = {Dropout: A Simple Way to Prevent Neural Networks from Overfitting},
  journal = {Journal of Machine Learning Research},
  year    = {2014},
  volume  = {15},
  pages   = {1929-1958},
  url     = {http://jmlr.org/papers/v15/srivastava14a.html}
}


@article{Hinton2012,
  title={Deep Neural Networks for Acoustic Modeling in Speech Recognition},
  author={ Hinton,Geoffrey and Deng, Li and  Yu, Dong and Dahl,George   and  Mohamed,Abdel-rahman and Jaitly, Navdeep and Senior,  Andrew and Vanhoucke, Vincent and Nguyen, Patrick  and  Kingsbury, Brian and  Sainath, Tara},
  journal={IEEE Signal Processing Magazine},
  year={2012}
}

@article{Graves2014,
  title={Towards End-to-End Speech Recognition with Recurrent Neural Networks},
  author={Graves, Alex  and  Jaitly, Navdeep},
  journal={International Conference on Machine Learning},
  year={2014}
}

@article{Chorowski2014,
  title={End-to-end Continuous Speech Recognition using Attention-based Recurrent NN: First Results},
  author={ Chorowski, Jan, and Bahdanau, Dzmitry , and Cho, Kyunghyun , and Bengio, Yoshua },
  journal={Neural Information Processing Systems: Workshop Deep Learning and Representation Learning Workshop },
  year={2014}
}

@article{Sak2014,
  title={Long short-term memory recurrent neural network architectures  for large scale acoustic modeling},
  author={Sak, Hasim and  Senior, Andrew and Beaufays, Francoise },
  journal={Interspeech 2014},
  year={2014}
}

@article{Ko2015,
  title={Audio Augmentation for Speech Recognition},
  author={Tom, Ko and Vijayaditya, Peddinti and Daniel, Povey
  and Sanjeev, Khudanpur },
  journal={Interspeech 2015},
  year={2015}
}

@article{Tjandra2017,
  title={Listening while Speaking: Speech Chain by Deep Learning},
  author={Andros, Tjandra and Sakriani, Sakti and Satoshi, Nakamura },
  journal={ASRU 2017},
  year={2017}
}

@article{Tjandra2018,
  title={Machine Speech Chain with One-shot Speaker Adaptation},
  author={Andros, Tjandra and Sakriani, Sakti and Satoshi, Nakamura },
  journal={Interspeech 2018},
  year={2018}
}

@article{bahdanau2014neural,
  title={Neural machine translation by jointly learning to align and translate},
  author={Bahdanau, Dzmitry and Cho, Kyunghyun and Bengio, Yoshua},
  journal={arXiv preprint arXiv:1409.0473},
  year={2014}
}

@article{cho2014learning,
  title={Learning phrase representations using RNN encoder-decoder for statistical machine translation},
  author={Cho, Kyunghyun and Van Merri{\"e}nboer, Bart and Gulcehre, Caglar and Bahdanau, Dzmitry and Bougares, Fethi and Schwenk, Holger and Bengio, Yoshua},
  journal={arXiv preprint arXiv:1406.1078},
  year={2014}
}

@article{rush2015neural,
  title={A neural attention model for abstractive sentence summarization},
  author={Rush, Alexander M and Chopra, Sumit and Weston, Jason},
  journal={arXiv preprint arXiv:1509.00685},
  year={2015}
}

@article{micikevicius2017mixed,
  title={Mixed precision training},
  author={Micikevicius, Paulius and Narang, Sharan and Alben, Jonah and Diamos, Gregory and Elsen, Erich and Garcia, David and Ginsburg, Boris and Houston, Michael and Kuchaev, Oleksii and Venkatesh, Ganesh and others},
  journal={arXiv preprint arXiv:1710.03740},
  year={2017}
}

@ARTICLE{Britz:2017,
  author = {{Britz}, Denny and {Goldie}, Anna and {Luong}, Thang and {Le}, Quoc},
  title  = {Massive Exploration of Neural Machine Translation Architectures},
  journal = {ArXiv e-prints arXiv:1703.03906},
  archivePrefix   = "arXiv",
  eprinttype      = {arxiv},
  eprint          = {1703.03906},
  primaryClass    = "cs.CL",
  keywords        = {Computer Science - Computation and Language},
  year            = 2017,
  month           = mar
}

@inproceedings{abadi2016tensorflow,
  title={TensorFlow: A System for Large-Scale Machine Learning.},
  author={Abadi, Mart{\'\i}n and Barham, Paul and Chen, Jianmin and Chen, Zhifeng and Davis, Andy and Dean, Jeffrey and Devin, Matthieu and Ghemawat, Sanjay and Irving, Geoffrey and Isard, Michael and others},
  booktitle={OSDI},
  volume={16},
  pages={265--283},
  year={2016}
}

@article{tensor2tensor,
  author = {Ashish Vaswani and Samy Bengio and Eugene Brevdo and Francois Chollet and Aidan N. Gomez and Stephan Gouws and Llion Jones and  \L{}ukasz Kaiser and Nal Kalchbrenner and Niki Parmar and Ryan Sepassi and
    Noam Shazeer and Jakob Uszkoreit},
  title     = {Tensor2Tensor for Neural Machine Translation},
  journal   = {CoRR},
  volume    = {abs/1803.07416},
  year      = {2018},
  url       = {http://arxiv.org/abs/1803.07416},
}

@article{gehring2017convs2s,
  author          = {Gehring, Jonas, and Auli, Michael and Grangier, David and Yarats, Denis and Dauphin, Yann N},
  title           = "{Convolutional Sequence to Sequence Learning}",
  journal         = {ArXiv e-prints arXiv:1705.03122},
  archivePrefix   = "arXiv",
  eprinttype      = {arxiv},
  eprint          = {1705.03122},
  primaryClass    = "cs.CL",
  keywords        = {Computer Science - Computation and Language},
  year            = 2017,
  month           = May,
}

@inproceedings{chan2015,
  title={Listen, attend and spell},
  author={Chan, William and Jaitly, Navdeep and Le, Quoc V and Vinyals, Oriol},
  booktitle={Acoustics, Speech and Signal Processing (ICASSP), 2016 IEEE International Conference on},
  pages={5206--5210},
  year={2016},
  organization={IEEE}
}

@inproceedings{xu2015show,
  title={Show, attend and tell: Neural image caption generation with visual attention},
  author={Xu, Kelvin and Ba, Jimmy and Kiros, Ryan and Cho, Kyunghyun and Courville, Aaron and Salakhudinov, Ruslan and Zemel, Rich and Bengio, Yoshua},
  booktitle={International Conference on Machine Learning},
  pages={2048--2057},
  year={2015}
}

@incollection{Sutskever2014,
  title = {Sequence to Sequence Learning with Neural Networks},
  author = {Sutskever, Ilya and Vinyals, Oriol and Le, Quoc V},
  booktitle = {Advances in Neural Information Processing Systems 27},
  editor = {Z. Ghahramani and M. Welling and C. Cortes and N. D. Lawrence and K. Q. Weinberger},
  pages = {3104--3112},
  year = {2014},
  publisher = {Curran Associates, Inc.},
  url = {http://papers.nips.cc/paper/5346-sequence-to-sequence-learning-with-neural-networks.pdf}
}

@article{DeepSpeech2014,
  title     = {Deep Speech: Scaling up end-to-end speech recognition},
  author    = {Awni Y. Hannun and  Carl Case and  Jared Casper and Bryan Catanzaro and Greg Diamos and Erich Elsen and Ryan Prenger and  Sanjeev Satheesh and Shubho Sengupta and Adam Coates and   Andrew Y. Ng},
  journal   = {CoRR},
  volume    = {abs/1412.5567},
  year      = {2014},
  url       = {http://arxiv.org/abs/1412.5567},
  archivePrefix = {arXiv},
  eprint    = {1412.5567},
  timestamp = {Mon, 13 Aug 2018 16:48:07 +0200},
  biburl    = {https://dblp.org/rec/bib/journals/corr/HannunCCCDEPSSCN14},
  bibsource = {dblp computer science bibliography, https://dblp.org}
}

@inproceedings{DeepSpeech2,
 author = {Amodei, Dario and Ananthanarayanan, Sundaram and Anubhai, Rishita and Bai, Jingliang and Battenberg, Eric and Case, Carl and Casper, Jared and Catanzaro, Bryan and Cheng, Qiang and Chen, Guoliang and Chen, Jie and Chen, Jingdong and Chen, Zhijie and Chrzanowski, Mike and Coates, Adam and Diamos, Greg and Ding, Ke and Du, Niandong and Elsen, Erich and Engel, Jesse and Fang, Weiwei and Fan, Linxi and Fougner, Christopher and Gao, Liang and Gong, Caixia and Hannun, Awni and Han, Tony and Johannes, Lappi Vaino and Jiang, Bing and Ju, Cai and Jun, Billy and LeGresley, Patrick and Lin, Libby and Liu, Junjie and Liu, Yang and Li, Weigao and Li, Xiangang and Ma, Dongpeng and Narang, Sharan and Ng, Andrew and Ozair, Sherjil and Peng, Yiping and Prenger, Ryan and Qian, Sheng and Quan, Zongfeng and Raiman, Jonathan and Rao, Vinay and Satheesh, Sanjeev and Seetapun, David and Sengupta, Shubho and Srinet, Kavya and Sriram, Anuroop and Tang, Haiyuan and Tang, Liliang and Wang, Chong and Wang, Jidong and Wang, Kaifu and Wang, Yi and Wang, Zhijian and Wang, Zhiqian and Wu, Shuang and Wei, Likai and Xiao, Bo and Xie, Wen and Xie, Yan and Yogatama, Dani and Yuan, Bin and Zhan, Jun and Zhu, Zhenyao},
 title = {Deep Speech 2: End-to-end Speech Recognition in English and Mandarin},
 booktitle = {Proceedings of the 33rd International Conference on International Conference on Machine Learning - Volume 48},
 series = {ICML'16},
 year = {2016},
 location = {New York, NY, USA},
 pages = {173--182},
 numpages = {10},
 url = {http://dl.acm.org/citation.cfm?id=3045390.3045410},
 acmid = {3045410},
 publisher = {JMLR.org},
}

@inproceedings{prabhavalkar2017comparison,
  title={A comparison of sequence-to-sequence models for speech recognition},
  author={Prabhavalkar, Rohit and Rao, Kanishka and Sainath, Tara N and Li, Bo and Johnson, Leif and Jaitly, Navdeep},
  booktitle={Proc. Interspeech},
  pages={939--943},
  year={2017}
}

@article{chiu2017state,
  title={State-of-the-art speech recognition with sequence-to-sequence models},
  author={Chiu, Chung-Cheng and Sainath, Tara N and Wu, Yonghui and Prabhavalkar, Rohit and Nguyen, Patrick and Chen, Zhifeng and Kannan, Anjuli and Weiss, Ron J and Rao, Kanishka and Gonina, Katya and others},
  journal={arXiv preprint arXiv:1712.01769},
  year={2017}
}

@misc{NVMixed,
  title = {{NVIDA's Mixed-Precision Training - TensorFlow example}},
  howpublished = {\url{https://docs.nvidia.com/deeplearning/sdk/mixed-precision-training/#example_tensorflow}},
  author={NVIDIA},
  note = {Accessed: 2018-10-09},
  year={2018}
}

@article{gehring2017,
  title={Convolutional sequence to sequence learning},
  author={Gehring, Jonas and Auli, Michael and Grangier, David and Yarats, Denis and Dauphin, Yann N},
  journal={arXiv preprint arXiv:1705.03122},
  year={2017}
}

@article{collobert2016,
  title={Wav2letter: an end-to-end convnet-based speech recognition system},
  author={Collobert, Ronan and Puhrsch, Christian and Synnaeve, Gabriel},
  journal={arXiv preprint arXiv:1609.03193},
  year={2016}
}

@inproceedings{Zhang2016,
author={Ying Zhang and Mohammad Pezeshki and Philémon Brakel and Saizheng Zhang and César Laurent and Yoshua Bengio and Aaron Courville},
title={Towards End-to-End Speech Recognition with Deep Convolutional Neural Networks},
year=2016,
booktitle={Interspeech 2016},
doi={10.21437/Interspeech.2016-1446},
url={http://dx.doi.org/10.21437/Interspeech.2016-1446},
pages={410--414}
}

@inproceedings{Zhang2017,
  title={Very deep convolutional networks for end-to-end speech recognition},
  author={Zhang, Yu, and  Chan, William, and  Jaitly, Navdeep},
  booktitle={Acoustics, Speech and Signal Processing (ICASSP), 2017 IEEE International Conference on},
  year={2017},
  organization={IEEE}
}


@article{Wang2017,
  title={Tacotron: Towards End-to-End Speech Synthesis},
  author={ Wang, Yuxuan, and Skerry-Ryan, RJ,  and Stanton, Daisy  and Wu, Yonghui and   Weiss, Ron, and   Jaitly, Navdeep  and Yang, Zongheng and  Xiao, Ying and  Chen,Zhifeng and Bengio, Samy and  Le, Quoc  and   Agiomyrgiannakis, Yannis  and  Clark,Rob and Saurous,  Rif A.},
  journal={arXiv preprint arXiv:1703.10135},
  year={2017}
}

@article{griffin1984signal,
  title={Signal estimation from modified short-time Fourier transform},
  author={Griffin, Daniel and Lim, Jae},
  journal={IEEE Transactions on Acoustics, Speech, and Signal Processing},
  volume={32},
  number={2},
  pages={236--243},
  year={1984},
  publisher={IEEE}
}

@misc{ito2017lj,
  title={The LJ speech dataset},
  author={Ito, Keith and others},
  year={2017}
}

@misc{mailabs,
  title = {{The M-AILABS Speech Dataset}},
  howpublished = {\url{http://www.m-ailabs.bayern/en/the-mailabs-speech-dataset/}},
  author={M-AILABS},
  note = {Accessed: 2018-10-09},
  year={2018}
}

@article{merity2016pointer,
  title={Pointer sentinel mixture models},
  author={Merity, Stephen and Xiong, Caiming and Bradbury, James and Socher, Richard},
  journal={arXiv preprint arXiv:1609.07843},
  year={2016}
}

@inproceedings{socher2013recursive,
  title={Recursive deep models for semantic compositionality over a sentiment treebank},
  author={Socher, Richard and Perelygin, Alex and Wu, Jean and Chuang, Jason and Manning, Christopher D and Ng, Andrew and Potts, Christopher},
  booktitle={Proceedings of the 2013 conference on empirical methods in natural language processing},
  pages={1631--1642},
  year={2013}
}

@InProceedings{maas-EtAl:2011:ACL-HLT2011,
  author    = {Maas, Andrew L.  and  Daly, Raymond E.  and  Pham, Peter T.  and  Huang, Dan  and  Ng, Andrew Y.  and  Potts, Christopher},
  title     = {Learning Word Vectors for Sentiment Analysis},
  booktitle = {Proceedings of the 49th Annual Meeting of the Association for Computational Linguistics: Human Language Technologies},
  month     = {June},
  year      = {2011},
  address   = {Portland, Oregon, USA},
  publisher = {Association for Computational Linguistics},
  pages     = {142--150},
  url       = {http://www.aclweb.org/anthology/P11-1015}
}

@inproceedings{Povey2018SemiOrthogonalLM,
  title={Semi-Orthogonal Low-Rank Matrix Factorization for Deep Neural Networks},
  author={Daniel Povey and Gaofeng Cheng and Yiming Wang and Ke Li and Hainan Xu and Mahsa Yarmohammadi and Sanjeev Khudanpur},
  booktitle={Interspeech},
  year={2018}
}

@article{CAPIO2017,
  author    = {Kyu J. Han and Akshay Chandrashekaran and Jungsuk Kim and  Ian R. Lane},
  title     = {The {CAPIO} 2017 Conversational Speech Recognition System},
  journal   = {CoRR},
  volume    = {abs/1801.00059},
  year      = {2018},
  url       = {http://arxiv.org/abs/1801.00059},
  archivePrefix = {arXiv},
  eprint    = {1801.00059},
  timestamp = {Mon, 13 Aug 2018 16:49:10 +0200},
  biburl    = {https://dblp.org/rec/bib/journals/corr/abs-1801-00059},
  bibsource = {dblp computer science bibliography, https://dblp.org}
}

@article{WaveNet,
  author    = {A{\"{a}}ron van den Oord and Sander Dieleman and  Heiga Zen and Karen Simonyan and                Oriol Vinyals and  Alex Graves and  Nal Kalchbrenner and Andrew W. Senior and Koray Kavukcuoglu},
  title     = {WaveNet: {A} Generative Model for Raw Audio},
  journal   = {CoRR},
  volume    = {abs/1609.03499},
  year      = {2016},
  url       = {http://arxiv.org/abs/1609.03499},
  archivePrefix = {arXiv},
  eprint    = {1609.03499},
  timestamp = {Mon, 13 Aug 2018 16:49:15 +0200},
  biburl    = {https://dblp.org/rec/bib/journals/corr/OordDZSVGKSK16},
  bibsource = {dblp computer science bibliography, https://dblp.org}
}

@article{FacebookGERENGBackTranslation,
  author    = {Rico Sennrich and Barry Haddow and Alexandra Birch},
  title     = {Improving Neural Machine Translation Models with Monolingual Data},
  journal   = {CoRR},
  volume    = {abs/1511.06709},
  year      = {2015},
  url       = {http://arxiv.org/abs/1511.06709},
  archivePrefix = {arXiv},
  eprint    = {1511.06709},
  timestamp = {Mon, 13 Aug 2018 16:47:05 +0200},
  biburl    = {https://dblp.org/rec/bib/journals/corr/SennrichHB15a},
  bibsource = {dblp computer science bibliography, https://dblp.org}
}

@article{GlobalStyleTokens,
  author    = {Yuxuan Wang and Daisy Stanton and  Yu Zhang and  R. J. Skerry{-}Ryan and   Eric Battenberg and Joel Shor and  Ying Xiao and  Fei Ren and  Ye Jia and  Rif A. Saurous},
  title     = {Style Tokens: Unsupervised Style Modeling, Control and Transfer in End-to-End Speech Synthesis},
  journal   = {CoRR},
  volume    = {abs/1803.09017},
  year      = {2018},
  url       = {http://arxiv.org/abs/1803.09017},
  archivePrefix = {arXiv},
  eprint    = {1803.09017},
  timestamp = {Mon, 13 Aug 2018 16:46:53 +0200},
  biburl    = {https://dblp.org/rec/bib/journals/corr/abs-1803-09017},
  bibsource = {dblp computer science bibliography, https://dblp.org}
}

@article{IoffeS15BatchNorm,
  author = {Sergey Ioffe and  Christian Szegedy},
  title  = {Batch Normalization: Accelerating Deep Network Training by Reducing Internal Covariate Shift},
  journal = {CoRR},
  volume = {abs/1502.03167},
  year = {2015},
  url  = {http://arxiv.org/abs/1502.03167},
  archivePrefix = {arXiv},
  eprint    = {1502.03167},
  timestamp = {Mon, 13 Aug 2018 16:47:06 +0200},
  biburl    = {https://dblp.org/rec/bib/journals/corr/IoffeS15},
  bibsource = {dblp computer science bibliography, https://dblp.org}
}

@article{kingma,
  author    = {Diederik P. Kingma and
               Jimmy Ba},
  title     = {Adam: {A} Method for Stochastic Optimization},
  journal   = {CoRR},
  volume    = {abs/1412.6980},
  year      = {2014},
  url       = {http://arxiv.org/abs/1412.6980},
  archivePrefix = {arXiv},
  eprint    = {1412.6980},
  timestamp = {Mon, 13 Aug 2018 01:00:00 +0200},
  biburl    = {https://dblp.org/rec/bib/journals/corr/KingmaB14},
  bibsource = {dblp computer science bibliography, https://dblp.org}
}

@incollection{Salimans2016WeightNorm,
  title = {Weight Normalization: A Simple Reparameterization to Accelerate Training of Deep Neural Networks},
  author = {Salimans, Tim and Kingma, Durk P},
  booktitle = {Advances in Neural Information Processing Systems 29},
  editor = {D. D. Lee and M. Sugiyama and U. V. Luxburg and I. Guyon and R. Garnett},
  pages = {901--909},
  year = {2016},
  publisher = {Curran Associates, Inc.},
  url = {http://papers.nips.cc/paper/6114-weight-normalization-a-simple-reparameterization-to-accelerate-training-of-deep-neural-networks.pdf}
}

@article{wu2016google,
  title={Google's neural machine translation system: Bridging the gap between human and machine translation},
  author={Wu, Yonghui and Schuster, Mike and Chen, Zhifeng and Le, Quoc V and Norouzi, Mohammad and Macherey, Zolfgang and Krikun, Maxim and Cao, Yuan and Gao, Qin and Macherey, Klaus and others},
  journal={arXiv preprint arXiv:1609.08144},
  year={2016}
}

@inproceedings{opennmt,
  author = {Guillaume Klein and  Yoon Kim and  Yuntian Deng  and  Jean Senellart and  Alexander M. Rush},
  title  = {OpenNMT: Open-Source Toolkit for Neural Machine Translation},
  booktitle = {Proc. ACL},
  year      = {2017},
  url       = {https://doi.org/10.18653/v1/P17-4012},
  doi       = {10.18653/v1/P17-4012}
}

@article{paszke2017automatic,
  title={Automatic differentiation in PyTorch},
  author={Paszke, Adam and Gross, Sam and Chintala, Soumith and Chanan, Gregory and Yang, Edward and DeVito, Zachary and Lin, Zeming and Desmaison, Alban and Antiga, Luca and Lerer, Adam},
  year={2017}
}

@article{yu2014introduction,
  title={An introduction to computational networks and the computational network toolkit},
  author={Yu, Dong and Eversole, Adam and Seltzer, Mike and Yao, Kaisheng and Huang, Zhiheng and Guenter, Brian and Kuchaiev, Oleksii and Zhang, Yu and Seide, Frank and Wang, Huaming and others},
  journal={Microsoft Technical Report MSR-TR-2014--112},
  year={2014}
}

@article{nvidia2017v100,
  title={V100 GPU architecture. The world’s most advanced data center GPU. Version WP-08608-001\_v1. 1},
  author={NVIDIA, Tesla},
  journal={NVIDIA. Aug},
  pages={108},
  year={2017}
}

@article{Ba2016LayerNorm,
  author    = {Jimmy Lei Ba and Jamie Ryan Kiros and Geoffrey E Hinton},
  title     = {Layer normalization},
  journal   = {CoRR},
  volume    = {abs/1607.06450},
  year      = {2016},
  url       = {http://arxiv.org/abs/1607.06450},
  archivePrefix = {arXiv},
}

@inproceedings{Dauphin2017GLU,
 author = {Dauphin, Yann N. and Fan, Angela and Auli, Michael and Grangier, David},
 title = {Language Modeling with Gated Convolutional Networks},
 booktitle = {Proceedings of the 34th International Conference on Machine Learning - Volume 70},
 series = {ICML'17},
 year = {2017},
 location = {Sydney, NSW, Australia},
 pages = {933--941},
 numpages = {9},
 url = {http://dl.acm.org/citation.cfm?id=3305381.3305478},
 acmid = {3305478},
 publisher = {JMLR.org},
}

@incollection{Oord2016PixelCNN,
title = {Conditional Image Generation with PixelCNN Decoders},
author = {van den Oord, Aaron and Kalchbrenner, Nal and Espeholt, Lasse and kavukcuoglu, koray and Vinyals, Oriol and Graves, Alex},
booktitle = {Advances in Neural Information Processing Systems 29},
editor = {D. D. Lee and M. Sugiyama and U. V. Luxburg and I. Guyon and R. Garnett},
pages = {4790--4798},
year = {2016},
publisher = {Curran Associates, Inc.},
url = {http://papers.nips.cc/paper/6527-conditional-image-generation-with-pixelcnn-decoders.pdf}
}

@article{he2015,
  title={Deep residual learning for image recognition},
  author={K. He, and X. Zhang, and S. Ren, and J. Sun},
  journal={arXiv preprint arXiv:1512.03385},
  year={2015}
}

@article{huang2016,
  title={Densely Connected Convolutional Networks},
  author={Gao Huang, and Zhuang Liu, and Laurens van der Maaten, and Kilian Q. Weinberger},
  journal={arXiv preprint arXiv:1608.06993},
  year={2016}
}

@inproceedings{heafield2011kenlm,
  title={KenLM: Faster and smaller language model queries},
  author={Heafield, Kenneth},
  booktitle={Proceedings of the sixth workshop on statistical machine translation},
  pages={187--197},
  year={2011},
  organization={Association for Computational Linguistics}
}

@article{dai2018transformer,
  title={Transformer-XL: Language Modeling with Longer-Term Dependency},
  author={Dai, Zihang and Yang, Zhilin and Yang, Yiming and Cohen, William W and Carbonell, Jaime and Le, Quoc V and Salakhutdinov, Ruslan},
  year={2018},
  journal   = {CoRR},
  volume    = {abs/1901.02860},
  url       = {http://arxiv.org/abs/1901.02860},
  archivePrefix = {arXiv},
  eprint    = {1901.02860},
  timestamp = {Fri, 01 Feb 2019 13:39:59 +0100},
  biburl    = {https://dblp.org/rec/bib/journals/corr/abs-1901-02860},
  bibsource = {dblp computer science bibliography, https://dblp.org}
}

@inproceedings{Saon+2016,
author={George Saon and Tom Sercu and Steven Rennie and Hong-Kwang J. Kuo},
title={The IBM 2016 English Conversational Telephone Speech Recognition System},
year=2016,
booktitle={Interspeech 2016},
doi={10.21437/Interspeech.2016-1460},
url={http://dx.doi.org/10.21437/Interspeech.2016-1460},
pages={7--11}
}

@INPROCEEDINGS{Sercu-2016,
author={T. {Sercu} and C. {Puhrsch} and B. {Kingsbury} and Y. {LeCun}},
booktitle={2016 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)},
title={Very deep multilingual convolutional neural networks for LVCSR},
year={2016},
volume={},
number={},
pages={4955-4959},
keywords={natural language processing;neural nets;speech recognition;very deep multilingual convolutional neural networks;LVCSR;CNN;large vocabulary continuous speech recognition systems;word error rate;Training;Context;Hidden Markov models;Neural networks;Computer architecture;Kernel;Training data;Convolutional Networks;Multilingual;Acoustic Modeling;Speech Recognition;Neural Networks},
doi={10.1109/ICASSP.2016.7472620},
ISSN={2379-190X},
month={March},}


@inproceedings{Sercu+2016,
author={Tom Sercu and Vaibhava Goel},
title={Advances in Very Deep Convolutional Neural Networks for LVCSR},
year=2016,
booktitle={Interspeech 2016},
doi={10.21437/Interspeech.2016-1033},
url={http://dx.doi.org/10.21437/Interspeech.2016-1033},
pages={3429--3433}
}

@INPROCEEDINGS{Xiong-2018,
author={W. {Xiong} and L. {Wu} and F. {Alleva} and J. {Droppo} and X. {Huang} and A. {Stolcke}},
booktitle={2018 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)},
title={The Microsoft 2017 Conversational Speech Recognition System},
year={2018},
volume={},
number={},
pages={5934-5938},
keywords={convolution;feedforward neural nets;natural language processing;speaker recognition;speech processing;language model rescoring step;senone level;switchboard domains;character-based LSTM language models;NIST 2000 switchboard test set;frame level;word-level voting;acoustic model posteriors;dialog session aware LSTM language models;CNN-BLSTM acoustic model;Microsoft 2017 conversational speech recognition system;Acoustics;Error analysis;Training;Speech recognition;Switches;Computational modeling;Context modeling;Conversational speech recognition;CNN;LACE;BLSTM;LSTM-LM;system combination;human parity},
doi={10.1109/ICASSP.2018.8461870},
ISSN={2379-190X},
month={April},}

@inproceedings{zeyer2018improved,
  author={Albert Zeyer and Kazuki Irie and Ralf Schlüter and Hermann Ney},
  title={Improved Training of End-to-end Attention Models for Speech Recognition},
  year=2018,
  booktitle={Proc. Interspeech 2018},
  pages={7--11},
  doi={10.21437/Interspeech.2018-1616},
  url={http://dx.doi.org/10.21437/Interspeech.2018-1616}
}

@article{Wav2LetterV2,
  author    = {Vitaliy Liptchinsky and
               Gabriel Synnaeve and
               Ronan Collobert},
  title     = {Letter-Based Speech Recognition with Gated ConvNets},
  journal   = {CoRR},
  volume    = {abs/1712.09444},
  year      = {2017},
  url       = {http://arxiv.org/abs/1712.09444},
  archivePrefix = {arXiv},
  eprint    = {1712.09444},
  timestamp = {Mon, 13 Aug 2018 16:46:33 +0200},
  biburl    = {https://dblp.org/rec/bib/journals/corr/abs-1712-09444},
  bibsource = {dblp computer science bibliography, https://dblp.org}
}

@article{zeghidour2018,
  author    = {Neil Zeghidour and
               Qiantong Xu and
               Vitaliy Liptchinsky and
               Nicolas Usunier and
               Gabriel Synnaeve and
               Ronan Collobert},
  title     = {Fully Convolutional Speech Recognition},
  journal   = {CoRR},
  volume    = {abs/1812.06864},
  year      = {2018},
  url       = {http://arxiv.org/abs/1812.06864},
  archivePrefix = {arXiv},
  eprint    = {1812.06864},
  timestamp = {Tue, 01 Jan 2019 15:01:25 +0100},
  biburl    = {https://dblp.org/rec/bib/journals/corr/abs-1812-06864},
  bibsource = {dblp computer science bibliography, https://dblp.org}
}

@inproceedings{Hadian2018,
  author={Hossein Hadian and Hossein Sameti and Daniel Povey and Sanjeev Khudanpur},
  title={End-to-end Speech Recognition Using Lattice-free MMI},
  year=2018,
  booktitle={Proc. Interspeech 2018},
  pages={12--16},
  doi={10.21437/Interspeech.2018-1423},
  url={http://dx.doi.org/10.21437/Interspeech.2018-1423}
}

@inproceedings{Tang2018,
  author={Jian Tang and Yan Song and Lirong Dai and Ian McLoughlin},
  title={Acoustic Modeling with Densely Connected Residual Network for Multichannel Speech Recognition},
  year=2018,
  booktitle={Proc. Interspeech 2018},
  pages={1783--1787},
  doi={10.21437/Interspeech.2018-1089},
  url={http://dx.doi.org/10.21437/Interspeech.2018-1089}
}

@article{Kurata2017LanguageMW,
  title={Language modeling with highway LSTM},
  author={Gakuto Kurata and Bhuvana Ramabhadran and George Saon and Abhinav Sethy},
  journal={2017 IEEE Automatic Speech Recognition and Understanding Workshop (ASRU)},
  year={2017},
  pages={244-251}
}

@inproceedings{Saon2017,
  author={George Saon and Gakuto Kurata and Tom Sercu and Kartik Audhkhasi and Samuel Thomas and Dimitrios Dimitriadis and Xiaodong Cui and Bhuvana Ramabhadran and Michael Picheny and Lynn-Li Lim and Bergul Roomi and Phil Hall},
  title={English Conversational Telephone Speech Recognition by Humans and Machines},
  year=2017,
  booktitle={Proc. Interspeech 2017},
  pages={132--136},
  doi={10.21437/Interspeech.2017-405},
  url={http://dx.doi.org/10.21437/Interspeech.2017-405}
}

@inproceedings{Povey+2016,
author={Daniel Povey and Vijayaditya Peddinti and Daniel Galvez and Pegah Ghahremani and Vimal Manohar and Xingyu Na and Yiming Wang and Sanjeev Khudanpur},
title={Purely Sequence-Trained Neural Networks for ASR Based on Lattice-Free MMI},
year=2016,
booktitle={Interspeech 2016},
doi={10.21437/Interspeech.2016-595},
url={http://dx.doi.org/10.21437/Interspeech.2016-595},
pages={2751--2755}
}

@article{Yang2018,
  author    = {Xuerui Yang and
               Jiwei Li and
               Xi Zhou},
  title     = {A novel pyramidal-FSMN architecture with lattice-free {MMI} for speech
               recognition},
  journal   = {CoRR},
  volume    = {abs/1810.11352},
  year      = {2018},
  url       = {http://arxiv.org/abs/1810.11352},
  archivePrefix = {arXiv},
  eprint    = {1810.11352},
  timestamp = {Wed, 31 Oct 2018 14:24:29 +0100},
  biburl    = {https://dblp.org/rec/bib/journals/corr/abs-1810-11352},
  bibsource = {dblp computer science bibliography, https://dblp.org}
}

@article{liptchinsky2017based,
  title={Letter-Based Speech Recognition with Gated ConvNets},
  author={Liptchinsky, Vitaliy and Synnaeve, Gabriel and Collobert, Ronan},
  journal={arXiv preprint arXiv:1712.09444},
  year={2017}
}

@inproceedings{Weng2018,
  author={Chao Weng and Jia Cui and Guangsen Wang and Jun Wang and Chengzhu Yu and Dan Su and Dong Yu},
  title={Improving Attention Based Sequence-to-Sequence Models for End-to-End English Conversational Speech Recognition},
  year=2018,
  booktitle={Proc. Interspeech 2018},
  pages={761--765},
  doi={10.21437/Interspeech.2018-1030},
  url={http://dx.doi.org/10.21437/Interspeech.2018-1030}
}

@INPROCEEDINGS{Battenberg2017,
author={E. {Battenberg} and J. {Chen} and R. {Child} and A. {Coates} and Y. G. Y. {Li} and H. {Liu} and S. {Satheesh} and A. {Sriram} and Z. {Zhu}},
booktitle={2017 IEEE Automatic Speech Recognition and Understanding Workshop (ASRU)},
title={Exploring neural transducers for end-to-end speech recognition},
year={2017},
volume={},
number={},
pages={206-213},
keywords={recurrent neural nets;speech recognition;Hub500 benchmark;CTC models;speech recognition pipeline;RNN-Transducer models;language model;Seq2Seq models;end-to-end speech recognition;neural transducers;Decoding;Hidden Markov models;Transducers;Task analysis;Speech;Mathematical model;Neural networks},
doi={10.1109/ASRU.2017.8268937},
ISSN={},
month={Dec},
}

@inproceedings{
loshchilov2018,
title={Decoupled Weight Decay Regularization},
author={Ilya Loshchilov and Frank Hutter},
booktitle={International Conference on Learning Representations},
year={2019},
url={https://openreview.net/forum?id=Bkg6RiCqY7},
}

@article{zhang2017ndadam,
  author = {Zijun Zhang and Lin Ma and Zongpeng Li and Chuan Wu},
  title = {Normalized Direction-preserving Adam},
  journal = {arXiv e-prints arXiv:1709.04546},
  year = {2017},
}

@article{park2019,
       author = {{Park}, Daniel S. and {Chan}, William and {Zhang}, Yu and
         {Chiu}, Chung-Cheng and {Zoph}, Barret and {Cubuk}, Ekin D. and
         {Le}, Quoc V.},
        title = "{SpecAugment: A Simple Data Augmentation Method for Automatic Speech Recognition}",
      journal = {arXiv e-prints},
      year = "2019",
      eid = {arXiv:1904.08779},
      eprint = {1904.08779},
}

@article{novograd2019,
       author = {{Ginsburg}, Boris and {Castonguay}, Patrice and {Hrinchuk}, Oleksii and
         {Kuchaiev}, Oleksii and {Lavrukhin}, Vitaly and {Leary}, Ryan and
         {Li}, Jason and {Nguyen}, Huyen and {Cohen}, Jonathan M.},
        title = "{Stochastic Gradient Methods with Layer-wise Adaptive Moments for Training of Deep Networks}",
      journal = {arXiv e-prints},
         year = "2019",
          eid = {arXiv:1905.11286},
       eprint = {1905.11286},
}

@article{kriman2019quartznet,
  title={Quartznet: {Deep} automatic speech recognition with 1d time-channel separable convolutions},
  author={Kriman, Samuel and Beliaev, Stanislav and Ginsburg, Boris and Huang, Jocelyn and Kuchaiev, Oleksii and Lavrukhin, Vitaly and Leary, Ryan and Li, Jason and Zhang, Yang},
  journal={arXiv preprint arXiv:1910.10261},
  year={2019}
}

@misc{itu1988g711,
    title={{ITU-T} {G.711} - {Pulse} code modulation ({PCM}) of voice frequencies},
    author={ITU-T Geneva Switzerland},
    year={1988},
}

@article{han2020contextnet,
  title={ContextNet: Improving convolutional neural networks for automatic speech recognition with global context},
  author={Han, Wei and Zhang, Zhengdong and Zhang, Yu and Yu, Jiahui and Chiu, Chung-Cheng and Qin, James and Gulati, Anmol and Pang, Ruoming and Wu, Yonghui},
  journal={arXiv:2005.03191},
  year={2020}
}

@inproceedings{hu2018squeeze,
  title={Squeeze-and-excitation networks},
  author={Hu, Jie and Shen, Li and Sun, Gang},
  booktitle={ICVPR},
  year={2018}
}

@article{koluguri2020speakernet,
  title={SpeakerNet: 1D Depth-wise Separable Convolutional Network for Text-Independent Speaker Recognition and Verification},
  author={Koluguri, Nithin Rao and Li, Jason and Lavrukhin, Vitaly and Ginsburg, Boris},
  journal={arXiv preprint arXiv:2010.12653},
  year={2020}
}

@article{gulati2020conformer,
  title={Conformer: Convolution-augmented transformer for speech recognition},
  author={Gulati, Anmol and Qin, James and Chiu, Chung-Cheng and Parmar, Niki and Zhang, Yu and Yu, Jiahui and Han, Wei and Wang, Shibo and Zhang, Zhengdong and Wu, Yonghui and others},
  journal={arXiv preprint arXiv:2005.08100},
  year={2020}
}

@article{koluguri2021titanet,
  title={TitaNet: Neural Model for speaker representation with 1D Depth-wise separable convolutions and global context},
  author={Koluguri, Nithin Rao and Park, Taejin and Ginsburg, Boris},
  journal={arXiv preprint arXiv:2110.04410},
  year={2021}
}

@article{Dawalatabad_2021,
   title={ECAPA-TDNN Embeddings for Speaker Diarization},
   url={http://dx.doi.org/10.21437/Interspeech.2021-941},
   DOI={10.21437/interspeech.2021-941},
   journal={Interspeech 2021},
   publisher={ISCA},
   author={Dawalatabad, Nauman and Ravanelli, Mirco and Grondin, François and Thienpondt, Jenthe and Desplanques, Brecht and Na, Hwidong},
   year={2021},
   month={Aug}
}

@inproceedings{he2019streaming,
  title={Streaming end-to-end speech recognition for mobile devices},
  author={He, Yanzhang and Sainath, Tara N and Prabhavalkar, Rohit and McGraw, Ian and Alvarez, Raziel and Zhao, Ding and Rybach, David and Kannan, Anjuli and Wu, Yonghui and Pang, Ruoming and others},
  booktitle={ICASSP 2019-2019 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)},
  pages={6381--6385},
  year={2019},
  organization={IEEE}
}

@misc{wav2vec2,
  doi = {10.48550/ARXIV.2006.11477},
  url = {https://arxiv.org/abs/2006.11477},
  author = {Baevski, Alexei and Zhou, Henry and Mohamed, Abdelrahman and Auli, Michael},
  title = {wav2vec 2.0: A Framework for Self-Supervised Learning of Speech Representations},
  publisher = {arXiv},
  year = {2020},
  copyright = {arXiv.org perpetual, non-exclusive license}
}

@misc{w2v_bert,
  doi = {10.48550/ARXIV.2108.06209},
  url = {https://arxiv.org/abs/2108.06209},
  author = {Chung, Yu-An and Zhang, Yu and Han, Wei and Chiu, Chung-Cheng and Qin, James and Pang, Ruoming and Wu, Yonghui},
  title = {W2v-BERT: Combining Contrastive Learning and Masked Language Modeling for Self-Supervised Speech Pre-Training},
  publisher = {arXiv},
  year = {2021},
  copyright = {arXiv.org perpetual, non-exclusive license}
}

@misc{ssl_inter,
  doi = {10.48550/ARXIV.2112.08778},
  url = {https://arxiv.org/abs/2112.08778},
  author = {Wang, Chengyi and Wu, Yu and Chen, Sanyuan and Liu, Shujie and Li, Jinyu and Qian, Yao and Yang, Zhenglu},
  title = {Self-Supervised Learning for speech recognition with Intermediate layer supervision},
  publisher = {arXiv},
  year = {2021},
  copyright = {arXiv.org perpetual, non-exclusive license}
}

@misc{kim2022squeezeformer,
  doi = {10.48550/ARXIV.2206.00888},
  url = {https://arxiv.org/abs/2206.00888},
  author = {Kim, Sehoon and Gholami, Amir and Shaw, Albert and Lee, Nicholas and Mangalam, Karttikeya and Malik, Jitendra and Mahoney, Michael W. and Keutzer, Kurt},
  keywords = {Audio and Speech Processing (eess.AS), Computation and Language (cs.CL), Sound (cs.SD), FOS: Electrical engineering, electronic engineering, information engineering, FOS: Electrical engineering, electronic engineering, information engineering, FOS: Computer and information sciences, FOS: Computer and information sciences},
  title = {Squeezeformer: An Efficient Transformer for Automatic Speech Recognition},
  publisher = {arXiv},
  year = {2022},
  copyright = {arXiv.org perpetual, non-exclusive license}
}

@misc{park2022multi,
    doi = {10.48550/ARXIV.2203.15974},
    url = {https://arxiv.org/abs/2203.15974},
    author = {Park, Tae Jin and Koluguri, Nithin Rao and Balam, Jagadeesh and Ginsburg, Boris},
    keywords = {Audio and Speech Processing (eess.AS), Computation and Language (cs.CL), FOS: Electrical engineering, electronic engineering, information engineering, FOS: Electrical engineering, electronic engineering, information engineering, FOS: Computer and information sciences, FOS: Computer and information sciences},
    title = {Multi-scale Speaker Diarization with Dynamic Scale Weighting},
    publisher = {arXiv},
    year = {2022},
    copyright = {Creative Commons Attribution 4.0 International}
}

@inproceedings{vaswani2017aayn,
  title={Attention is all you need},
  author={Vaswani, Ashish and Shazeer, Noam and Parmar, Niki and Uszkoreit, Jakob and Jones, Llion and Gomez, Aidan N and Kaiser, {\L}ukasz and Polosukhin, Illia},
  booktitle={Advances in Neural Information Processing Systems},
  pages={6000--6010},
  year={2017}
}