All Checkpoints
===============
English
^^^^^^^
.. csv-table::
   :file: data/benchmark_en.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

German
^^^^^^
.. csv-table::
   :file: data/benchmark_de.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Spanish
^^^^^^^
.. csv-table::
   :file: data/benchmark_es.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

French
^^^^^^
.. csv-table::
   :file: data/benchmark_fr.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Russian
^^^^^^^
.. csv-table::
   :file: data/benchmark_ru.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Japanese
^^^^^^^^
.. csv-table::
   :file: data/benchmark_jp.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Georgian
^^^^^^^^
.. csv-table::
   :file: data/benchmark_ka.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Kazakh
^^^^^^
.. csv-table::
   :file: data/benchmark_kz.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Persian
^^^^^^^
.. csv-table::
   :file: data/benchmark_fa.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Uzbek
^^^^^
.. csv-table::
   :file: data/benchmark_uz.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Ukrainian
^^^^^^^^^
.. csv-table::
   :file: data/benchmark_ua.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Polish
^^^^^^
.. csv-table::
   :file: data/benchmark_pl.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Italian
^^^^^^^
.. csv-table::
   :file: data/benchmark_it.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Belarusian
^^^^^^^^^^
.. csv-table::
   :file: data/benchmark_by.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Croatian
^^^^^^^^
.. csv-table::
   :file: data/benchmark_hr.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Esperanto
^^^^^^^^^
.. csv-table::
   :file: data/benchmark_eo.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Kabyle
^^^^^^
.. csv-table::
   :file: data/benchmark_kab.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Dutch
^^^^^
.. csv-table::
   :file: data/benchmark_nl.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Catalan
^^^^^^^
.. csv-table::
   :file: data/benchmark_ca.csv
   :align: left
   :widths: 30,30,40
   :header-rows: 1

-----------------------------

Hindi
^^^^^^^
.. csv-table::
   :file: data/benchmark_hi.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Marathi
^^^^^^^
.. csv-table::
   :file: data/benchmark_mr.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

-----------------------------

Mandarin
^^^^^^^^
.. csv-table::
   :file: data/benchmark_zh.csv
   :align: left
   :widths: 50,50
   :header-rows: 1

Kinyarwanda
^^^^^^^^^^^
.. csv-table::
   :file: data/benchmark_rw.csv
   :align: left
   :widths: 50,50
   :header-rows: 1
