Resources and Documentation
---------------------------

Refer to `SSL-for-ASR notebook <https://github.com/NVIDIA/NeMo/tree/stable/tutorials/asr/Self_Supervised_Pre_Training.ipynb>`_
for a hands-on tutorial. If you are a beginner to NeMo, consider trying out the 
`ASR with NeMo <https://github.com/NVIDIA/NeMo/tree/stable/tutorials/asr/ASR_with_NeMo.ipynb>`_
tutorial. This and most other tutorials can be run on Google Colab by specifying the link to the 
notebooks' GitHub pages on Colab.

If you are looking for information about a particular ASR model, or would like to find out more 
about the model architectures available in the ``nemo_asr`` collection, refer to the 
:doc:`ASR Models <../models>` page.

NeMo includes preprocessing scripts for several common ASR datasets. The :doc:`ASR Datasets <../datasets>` 
page contains instructions on running those scripts. It also includes guidance for creating your 
own NeMo-compatible dataset, if you have your own data.

Information about how to load model checkpoints (either local files or pretrained ones from NGC), 
as well as a list of the checkpoints available on NGC are located on the :doc:`Checkpoints <./results>` 
page.

Documentation regarding the configuration files specific to the SSL can be found in the 
:doc:`Configuration Files <./configs>` page.
