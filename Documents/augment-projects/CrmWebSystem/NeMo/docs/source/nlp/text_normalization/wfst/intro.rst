WFST-based (Inverse) Text Normalization
=======================================

NeMo-text-processing supports Text Normalization (TN), audio-based TN and Inverse Text Normalization (ITN) tasks.

.. warning::

    TN/ITN transitioned from `NVIDIA/NeMo <https://github.com/NVIDIA/NeMo>`__ repository to a standalone `NVIDIA/NeMo-text-processing <https://github.com/NVIDIA/NeMo-text-processing>`__ repository. All updates and discussions/issues should go to the new repository.


WFST-based TN/ITN:

.. toctree::
   :maxdepth: 2

   wfst_text_normalization
   wfst_customization
   wfst_text_processing_deployment
   wfst_resources



