.. _wfst_resources:

Resources and Documentation
===========================

.. warning::

    *TN/ITN transitioned from [NVIDIA/NeMo](https://github.com/NVIDIA/NeMo) repository to a standalone [NVIDIA/NeMo-text-processing](https://github.com/NVIDIA/NeMo-text-processing) repository. All updates and discussions/issues should go to the new repository.*


- List of `TN/ITN issues <https://github.com/NVIDIA/NeMo/issues?q=is%3Aissue+label%3ATN%2FITN+>`_, use `TN/ITN` label
- TN/ITN related `discussions <https://github.com/NVIDIA/NeMo/discussions?discussions_q=label%3ATN%2FITN>`_, use `TN/ITN` label
- Documentation on how to generate :doc:`.far files for deployment in Riva (via Sparrowhawk) <./wfst_text_processing_deployment>`.
- Tutorial that provides an `Overview of NeMo-TN/ITN <https://colab.research.google.com/github/NVIDIA/NeMo-text-processing/blob/main/tutorials/Text_(Inverse)_Normalization.ipynb>`_
- Tutorial on `how to write new grammars <https://colab.research.google.com/github/NVIDIA/NeMo-text-processing/blob/main/tutorials/WFST_Tutorial.ipynb>`_ in `Pynini <https://www.opengrm.org/twiki/bin/view/GRM/Pynini>`_





