Adapters API
============

Core
----

.. autoclass:: nemo.core.adapter_mixins.AdapterModuleMixin
    :show-inheritance:
    :members:
    :member-order: bysource
    :undoc-members: adapter_module_names
    :noindex:

-----

.. autoclass:: nemo.core.adapter_mixins.AdapterModelPTMixin
    :show-inheritance:
    :members:
    :member-order: bysource
    :undoc-members: adapter_module_names
    :noindex:

-----

Adapter Networks
----------------


.. autoclass:: nemo.collections.common.parts.adapter_modules.AdapterModuleUtil
    :show-inheritance:
    :members:
    :member-order: bysource
    :noindex:

-----

.. autoclass:: nemo.collections.common.parts.adapter_modules.LinearAdapter
    :show-inheritance:
    :members:
    :member-order: bysource
    :noindex:

-----

Adapter Strategies
------------------


.. autoclass:: nemo.core.classes.mixins.adapter_mixin_strategies.AbstractAdapterStrategy
    :show-inheritance:
    :members:
    :member-order: bysource
    :undoc-members: adapter_module_names
    :noindex:

-----

.. autoclass:: nemo.core.classes.mixins.adapter_mixin_strategies.ReturnResultAdapterStrategy
    :show-inheritance:
    :members:
    :member-order: bysource
    :undoc-members: adapter_module_names
    :noindex:

-----

.. autoclass:: nemo.core.classes.mixins.adapter_mixin_strategies.ResidualAddAdapterStrategy
    :show-inheritance:
    :members:
    :member-order: bysource
    :undoc-members: adapter_module_names
    :noindex:
