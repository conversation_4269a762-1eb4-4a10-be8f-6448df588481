{
  "filename": "multimodal/nerf/configs.rst",
  "lineno": 4,
  "status": "broken",
  "code": 0,
  "uri": "../../core/core.html",
  "info": ""
}
{
  "filename": "multimodal/vlm/configs.rst",
  "lineno": 160,
  "status": "broken",
  "code": 0,
  "uri": "./clip.html#clip",
  "info": ""
}
{
  "filename": "multimodal/text2img/configs.rst",
  "lineno": 51,
  "status": "broken",
  "code": 0,
  "uri": "./datasets.html",
  "info": ""
}
{
  "filename": "multimodal/nerf/configs.rst",
  "lineno": 80,
  "status": "broken",
  "code": 0,
  "uri": "./datasets.html#Datasets",
  "info": ""
}
{
  "filename": "multimodal/nerf/configs.rst",
  "lineno": 125,
  "status": "broken",
  "code": 0,
  "uri": "./dreamfusion.html#dreamfusion",
  "info": ""
}
{
  "filename": "vision/configs.rst",
  "lineno": 133,
  "status": "broken",
  "code": 0,
  "uri": "./vit.html#vit",
  "info": ""
}
{
  "filename": "multimodal/text2img/configs.rst",
  "lineno": 12,
  "status": "broken",
  "code": 0,
  "uri": "PUTTHEURL",
  "info": ""
}
{
  "filename": "multimodal/mllm/configs.rst",
  "lineno": 143,
  "status": "broken",
  "code": 0,
  "uri": "./neva.html#neva",
  "info": ""
}
{
  "filename": "multimodal/text2img/datasets.rst",
  "lineno": 32,
  "status": "broken",
  "code": 0,
  "uri": "http://TODOURL",
  "info": "HTTPConnectionPool(host='todourl', port=80): Max retries exceeded with url: / (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x10fe3a270>: Failed to resolve 'todourl' ([Errno 8] nodename nor servname provided, or not known)\"))"
}
{
  "filename": "multimodal/mllm/datasets.rst",
  "lineno": 29,
  "status": "broken",
  "code": 0,
  "uri": "https://cocodataset.org/#download",
  "info": "Anchor 'download' not found"
}
{
  "filename": "multimodal/mllm/intro.rst",
  "lineno": 4,
  "status": "broken",
  "code": 0,
  "uri": "https://docs.nvidia.com/nemo-framework/user-guide/latest/multimodalmodels/index.html",
  "info": "404 Client Error: Not Found for url: https://docs.nvidia.com/nemo-framework/user-guide/latest/multimodalmodels/index.html"
}
{
  "filename": "multimodal/vlm/clip.rst",
  "lineno": 140,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/Megatron-LM#distributed-optimizer",
  "info": "Anchor 'distributed-optimizer' not found"
}
{
  "filename": "multimodal/mllm/neva.rst",
  "lineno": 132,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/Megatron-LM#distributed-pretraining",
  "info": "Anchor 'distributed-pretraining' not found"
}
{
  "filename": "checkpoints/dist_ckpt.rst",
  "lineno": 428,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/Megatron-LM/blob/main/megatron/core/optimizer/optimizer.py#L793",
  "info": "Anchor 'L793' not found"
}
{
  "filename": "tools/nemo_forced_aligner.rst",
  "lineno": 22,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/NeMo#installation",
  "info": "Anchor 'installation' not found"
}
{
  "filename": "multimodal/text2img/insp2p.rst",
  "lineno": 16,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/timothybrooks/instruct-pix2pix#generated-dataset",
  "info": "Anchor 'generated-dataset' not found"
}
{
  "filename": "multimodal/text2img/configs.rst",
  "lineno": 51,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/webdataset/webdataset#multinode-training",
  "info": "Anchor 'multinode-training' not found"
}
{
  "filename": "checkpoints/intro.rst",
  "lineno": 28,
  "status": "broken",
  "code": 0,
  "uri": "https://nvidia.github.io/TensorRT-LLM/architecture/checkpoint.html",
  "info": "404 Client Error: Not Found for url: https://nvidia.github.io/TensorRT-LLM/architecture/checkpoint.html"
}
{
  "filename": "multimodal/text2img/configs.rst",
  "lineno": 23,
  "status": "broken",
  "code": 0,
  "uri": "../api.html#Datasets",
  "info": ""
}
{
  "filename": "audio/configs.rst",
  "lineno": 17,
  "status": "broken",
  "code": 0,
  "uri": "./api.html#Datasets",
  "info": ""
}
