{
  "filename": "nlp/text_normalization/nn_text_normalization.rst",
  "lineno": 247,
  "status": "broken",
  "code": 0,
  "uri": "https://research.fb.com/wp-content/uploads/2019/03/Neural-Models-of-Text-Normalization-for-Speech-Applications.pdf",
  "info": "400 Client Error: Bad Request for url: https://research.facebook.com/wp-content/uploads/2019/03/Neural-Models-of-Text-Normalization-for-Speech-Applications.pdf"
}
{
  "filename": "asr/api.rst",
  "lineno": 7,
  "status": "broken",
  "code": 0,
  "uri": "http://www.ee.columbia.edu/~dpwe/resources/matlab/pvoc/",
  "info": "403 Client Error: Forbidden for url: https://www.ee.columbia.edu/~dpwe/resources/matlab/pvoc/"
}
{
  "filename": "asr/models.rst",
  "lineno": 113,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/NeMo/blob/main/nemo/collections/asr/modules/hybrid_autoregressive_transducer.py#L39",
  "info": "Anchor 'L39' not found"
}
{
  "filename": "nlp/bert_pretraining.rst",
  "lineno": 53,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/DeepLearningExamples/tree/master/PyTorch/LanguageModeling/BERT#quick-start-guide",
  "info": "Anchor 'quick-start-guide' not found"
}
{
  "filename": "nlp/text_normalization/wfst/wfst_customization.rst",
  "lineno": 17,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/NeMo-text-processing#from-source",
  "info": "Anchor 'from-source' not found"
}
{
  "filename": "nlp/nemo_megatron/gpt/gpt_training.rst",
  "lineno": 115,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/NeMo/blob/main/examples/llm/pretrain/README.md#run-pre-training-with-a-default-recipe",
  "info": "Anchor 'run-pre-training-with-a-default-recipe' not found"
}
{
  "filename": "nlp/nemo_megatron/gpt/gpt_training.rst",
  "lineno": 115,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/NeMo/blob/main/examples/llm/pretrain/README.md#create-and-run-a-custom-recipe",
  "info": "Anchor 'create-and-run-a-custom-recipe' not found"
}
{
  "filename": "nlp/joint_intent_slot.rst",
  "lineno": 155,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/NeMo/blob/stable/docs/source/nlp/nlp_model.rst#model-nlp",
  "info": "Anchor 'model-nlp' not found"
}
{
  "filename": "nlp/machine_translation/machine_translation.rst",
  "lineno": 242,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/NeMo/blob/v1.0.2/nemo/collections/nlp/data/machine_translation/machine_translation_dataset.py#L67",
  "info": "Anchor 'L67' not found"
}
{
  "filename": "nlp/punctuation_and_capitalization_lexical_audio.rst",
  "lineno": 255,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/NeMo/tree/stable/nemo/collections/common/parts/adapter_modules.py#L157",
  "info": "Anchor 'L157' not found"
}
{
  "filename": "nlp/question_answering.rst",
  "lineno": 196,
  "status": "broken",
  "code": 0,
  "uri": "https://msmarco.blob.core.windows.net/msmarco/dev_v2.1.json.gz",
  "info": "409 Client Error: Public access is not permitted on this storage account. for url: https://msmarco.blob.core.windows.net/msmarco/dev_v2.1.json.gz"
}
{
  "filename": "nlp/question_answering.rst",
  "lineno": 195,
  "status": "broken",
  "code": 0,
  "uri": "https://msmarco.blob.core.windows.net/msmarco/train_v2.1.json.gz",
  "info": "409 Client Error: Public access is not permitted on this storage account. for url: https://msmarco.blob.core.windows.net/msmarco/train_v2.1.json.gz"
}
{
  "filename": "nlp/language_modeling.rst",
  "lineno": 54,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/NeMo/tree/stable/nemo/collections/nlp/data/language_modeling/sentence_dataset.py#L35",
  "info": "Anchor 'L35' not found"
}
{
  "filename": "features/optimizations/activation_recomputation.rst",
  "lineno": 12,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/Megatron-LM/blob/main/megatron/core/transformer/transformer_config.py#L25",
  "info": "Anchor 'L25' not found"
}
{
  "filename": "features/optimizations/communication_overlap.rst",
  "lineno": 52,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/NeMo/blob/main/nemo/collections/llm/recipes/tp_overlap_configs/userbuffers.py#L64",
  "info": "Anchor 'L64' not found"
}
{
  "filename": "features/optimizations/communication_overlap.rst",
  "lineno": 52,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/NeMo/blob/main/nemo/lightning/pytorch/callbacks/megatron_comm_overlap.py#L61",
  "info": "Anchor 'L61' not found"
}
{
  "filename": "features/optimizations/attention_optimizations.rst",
  "lineno": 96,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/Megatron-LM/blob/main/megatron/core/transformer/attention.py#L89",
  "info": "Anchor 'L89' not found"
}
{
  "filename": "features/parallelisms.rst",
  "lineno": 319,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/Megatron-LM/blob/e2ec14ab5690fead7e33760b0f8fb20c83b4fd1f/megatron/core/transformer/moe/moe_layer.py#L29",
  "info": "Anchor 'L29' not found"
}
{
  "filename": "nlp/spellchecking_asr_customization.rst",
  "lineno": 38,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/NeMo/blob/stable/tutorials/nlp/SpellMapper_English_ASR_Customization.ipynb",
  "info": "404 Client Error: Not Found for url: https://github.com/NVIDIA/NeMo/blob/stable/tutorials/nlp/SpellMapper_English_ASR_Customization.ipynb"
}
{
  "filename": "nlp/glue_benchmark.rst",
  "lineno": 6,
  "status": "broken",
  "code": 0,
  "uri": "https://github.com/NVIDIA/NeMo/blob/stable/tutorials/nlp/GLUE_Benchmark.ipynb",
  "info": "404 Client Error: Not Found for url: https://github.com/NVIDIA/NeMo/blob/stable/tutorials/nlp/GLUE_Benchmark.ipynb"
}