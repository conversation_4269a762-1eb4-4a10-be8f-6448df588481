# Copyright (c) 2020-2021, NVIDIA CORPORATION.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
name: Secrets detector

on:
  pull_request_target:
    branches:
      - 'main'

jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.NEMO_REFORMAT_TOKEN }}

      - name: Install secrets detector
        run: pip install detect-secrets

      - name: Run on change-set
        run: |
          git diff --name-only --diff-filter=d --merge-base origin/main -z | xargs -0 detect-secrets-hook --disable-plugin Hex<PERSON><PERSON><PERSON>ntropyString --baseline .secrets.baseline
      
      - uses: EndBug/add-and-commit@v9
        # Commit changes. Nothing is committed if no changes.
        if: always()
        with:
          message: Update baseline
          commit: --signoff
