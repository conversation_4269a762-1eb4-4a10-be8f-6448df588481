name: <PERSON><PERSON>-Close-Inactive-Issues-PRs
on:
  schedule:
    - cron: "30 1 * * *"

jobs:
  close-issues:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v6
        with:
          operations-per-run: 100
          days-before-issue-stale: 30
          days-before-issue-close: 7 
          stale-issue-label: "stale"
          stale-issue-message: "This issue is stale because it has been open for 30 days with no activity. Remove stale label or comment or this will be closed in 7 days."
          close-issue-message: "This issue was closed because it has been inactive for 7 days since being marked as stale."
          days-before-pr-stale: 14 
          days-before-pr-close: 7 
          stale-pr-message: "This PR is stale because it has been open for 14 days with no activity. Remove stale label or comment or update or this will be closed in 7 days."
          close-pr-message: "This PR was closed because it has been inactive for 7 days since being marked as stale."
          repo-token: ${{ secrets.GITHUB_TOKEN }}
