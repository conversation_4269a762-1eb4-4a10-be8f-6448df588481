name: 'Changelog Build (Release)'

on:
  workflow_dispatch:
    inputs:
      last-release-tag:
        description: Last Git tag to start from (exclusive) (e.g. `v2.0.0`)
        type: string
        required: true
      release-branch:
        description: Release branch to build changelog on (e.g. `r2.1.0`)
        type: string
        required: true
    
jobs:
  changelog:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout branch
        uses: actions/checkout@v4
        with:
          ref: main
          fetch-depth: 0

      - name: Build Changelog
        id: github_tag
        uses: mikepenz/release-changelog-builder-action@v3.3.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          # Configuration file is setup with filters for domains
          # owner:repo must point to current repo
          # fromTag: Auto resolved from historical tag order (previous tag compared to current tag)
          # toTag: Current tag reference
          configuration: ".github/workflows/config/changelog-config.json"
          owner: "NVIDIA"
          repo: "NeMo"
          ignorePreReleases: "false"
          failOnError: "false"
          fromTag: ${{ inputs.last-release-tag }}
          toTag: ${{ inputs.release-branch }}

      - name: Update changelog file
        env: 
          RELEASE_BRANCH: ${{ inputs.release-branch }}
          CHANGELOG: ${{ steps.github_tag.outputs.changelog }}
        shell: bash -x -e -u -o pipefail {0}
        run: |
          RELEASE_VERSION=${RELEASE_BRANCH#r}
          CHANGELOG=$(echo "$CHANGELOG" | sed '/^[[:blank:]]*#/s/#/###/')

          RELEASE_NOTES="## NVIDIA Neural Modules $RELEASE_VERSION

          ### Detailed Changelogs:

          $CHANGELOG"

          printf "%s\n" "$RELEASE_NOTES" | sed '/<!-- Next changelog -->/r /dev/stdin' CHANGELOG.md > CHANGELOG.tmp.md

          mv CHANGELOG.tmp.md CHANGELOG.md

      - name: Inspect new changelog file
        run: cat CHANGELOG.md

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v7
        with:
          commit-message: "beep boop: Update changelog"
          title: "Update changelog for `${{ inputs.release-branch }}`"
          signoff: true
          sign-commits: true
          base: main
          branch: bot/chore/update-changelog-into-${{ inputs.release-branch }}
