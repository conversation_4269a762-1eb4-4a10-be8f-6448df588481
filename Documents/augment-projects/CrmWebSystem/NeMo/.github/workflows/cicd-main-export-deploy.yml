# Copyright (c) 2025, NVIDIA CORPORATION.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
name: NeMo E2E Export Deploy Tests
on:
  workflow_call:
    inputs:
      test_to_run:
        required: true
        type: string
      image-name:
        required: false
        default: nemo_container_export_deploy
        type: string

jobs:
  build:
    uses: ./.github/workflows/_build_container.yml
    with:
      image-name: ${{ inputs.image-name }}
      dockerfile: docker/Dockerfile.ci.export_deploy

  unit-tests:
    strategy:
      fail-fast: false
      matrix:
        include:
          - script: L0_Unit_Tests_GPU_Export_Deploy
            runner: self-hosted-azure
          - script: L0_Unit_Tests_CPU_Export_Deploy
            runner: self-hosted-azure-cpu
            cpu-only: true
          - script: L0_Unit_Tests_Eval_Legacy
            runner: self-hosted-azure
          - script: L0_Unit_Tests_Eval
            runner: self-hosted-azure
          - script: L0_Unit_Tests_Eval_Adapters
            runner: self-hosted-azure
    needs: [build]
    runs-on: ${{ matrix.runner }}
    name: ${{ matrix.script }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          path: ${{ github.run_id }}
      - name: main
        uses: NVIDIA/NeMo/.github/actions/test-template@main
        with:
          runner: ${{ runner.name }}
          script: ${{ matrix.script }}
          is_unit_test: true
          tests_to_run: ${{ inputs.test_to_run }}
          image: ${{ inputs.image-name }}
          cpu-only: ${{ matrix.cpu-only || false }}
  e2e-tests:
    strategy:
      fail-fast: false
      matrix:
        include:
          # Export tests
          - script: L2_NeMo_2_Export_HF_TRT_LLM
            runner: self-hosted-azure
          - script: L2_NeMo_2_Export_Deploy_Query_In_Framework
            runner: self-hosted-azure
          - script: L2_ONNX_TRT_LLM_Embedding_Export
            runner: self-hosted-azure
          - script: L2_NeMo_2_Export_TRT_LLM
            runner: self-hosted-azure
          - script: L2_NeMo_2_vLLM_Export_Llama
            runner: self-hosted-azure
          - script: L2_NeMo_2_vLLM_Export_Mixtral
            runner: self-hosted-azure
          - script: L2_NeMo_2_Export_In_Framework
            runner: self-hosted-azure
          - script: L2_NeMo_2_Export_Qnemo_TRT_LLM
            runner: self-hosted-azure
          - script: L2_NeMo_2_VLLM_VISION
            runner: self-hosted-azure
          - script: L2_NeMo_2_EVAL_Legacy
            runner: self-hosted-azure-gpus-1
          - script: L2_NeMo_2_EVAL_gsm8k
            runner: self-hosted-azure-gpus-1
          - script: L2_NeMo_2_EVAL_arcc
            runner: self-hosted-azure-gpus-1
    needs: [unit-tests]
    runs-on: ${{ matrix.runner }}
    name: ${{ matrix.is_optional && 'PLEASEFIXME_' || '' }}${{ matrix.script }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          path: ${{ github.run_id }}
      - name: main
        uses: NVIDIA/NeMo/.github/actions/test-template@main
        with:
          runner: ${{ runner.name }}
          script: ${{ matrix.script }}
          tests_to_run: ${{ inputs.test_to_run }}
          image: ${{ inputs.image-name }}
          is_optional: ${{ matrix.is_optional || false }}
