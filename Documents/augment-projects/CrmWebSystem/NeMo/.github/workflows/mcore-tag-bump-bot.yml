# Regularly updates the CI container
name: Megatron Tag Bump Bot
on:
  workflow_dispatch:
  schedule:
    - cron: 0 0 * * *

jobs:
  get-release-branch-names:
    runs-on: ubuntu-latest
    outputs:
      mcore: ${{ steps.get-branch.outputs.mcore_release_branch }}
      nemo: ${{ steps.get-branch.outputs.nemo_release_branch }}
    steps:
      - name: Get release branch names
        id: get-branch
        run: |
          latest_branch=$(git ls-remote --heads https://github.com/NVIDIA/Megatron-LM.git 'refs/heads/core_r*' | 
            grep -o 'core_r[0-9]\+\.[0-9]\+\.[0-9]\+' | 
            sort -V | 
            tail -n1)
          echo "mcore_release_branch=$latest_branch" >> $GITHUB_OUTPUT

          latest_branch=$(git ls-remote --heads https://github.com/NVIDIA/NeMo.git 'refs/heads/r*' | 
            grep -o 'r[0-9]\+\.[0-9]\+\.[0-9]\+' | 
            sort -V | 
            tail -n1)
          echo "nemo_release_branch=$latest_branch" >> $GITHUB_OUTPUT

  bump-tags:
    needs: [get-release-branch-names]
    strategy:
      fail-fast: false
      matrix:
        include:
          - nemo-target-branch: ${{ needs.get-release-branch-names.outputs.nemo }}
            mcore-target-branch: ${{ needs.get-release-branch-names.outputs.mcore }}
          - nemo-target-branch: main
            mcore-target-branch: main
    uses: ./.github/workflows/_bump_mcore_tag.yml
    with:
      nemo-target-branch: ${{ matrix.nemo-target-branch }}
      mcore-target-branch: ${{ matrix.mcore-target-branch }}
    secrets:
      PAT: ${{ secrets.PAT }}

  notify:
    if: failure()
    runs-on: ubuntu-latest
    needs: [bump-tags]
    steps:
      - name: Notify
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_WEBHOOK_ADMIN: <!subteam^${{ secrets.SLACK_WEBHOOK_ADMIN }}>
          GITHUB_RUN_ID: ${{ github.run_id }}
          GITHUB_REPOSITORY: ${{ github.repository }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\":robot_joy: <https://github.com/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}|Mcore-bump-bot workflow> failed. Please fix manually.\n\ncc ${SLACK_WEBHOOK_ADMIN}\"}" \
            $SLACK_WEBHOOK
