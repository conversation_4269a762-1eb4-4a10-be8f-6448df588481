name: Check __init__ files

on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  check-init-files:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Install init-file-checker
        run: pip install init-file-checker

      - name: Run init-file-checker
        run: init-file-checker nemo/
