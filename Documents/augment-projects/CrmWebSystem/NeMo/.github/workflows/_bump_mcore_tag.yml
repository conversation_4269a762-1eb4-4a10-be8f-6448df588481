name: ~Bump Megatron Tag template
on:
  workflow_call:
    inputs:
      nemo-target-branch:
        required: true
        type: string
        description: "The target branch to bump"
      mcore-target-branch:
        required: true
        type: string
        description: "The target branch to bump"
    secrets:
      PAT:
        required: true

jobs:
  update-branch:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          ref: ${{ inputs.nemo-target-branch }}

      - name: Set Git config
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "Github Actions"
      - name: Merge weekly-bump-${{ inputs.nemo-target-branch }} back to base branch
        env:
          SOURCE_BRANCH: weekly-bump-${{ inputs.nemo-target-branch }}
          TARGET_BRANCH: ${{ inputs.nemo-target-branch }}
        run: |
          if git ls-remote --exit-code origin $SOURCE_BRANCH; then
            git fetch --unshallow
            git checkout $SOURCE_BRANCH
            git pull
            git merge --no-ff $TARGET_BRANCH -m "chore: Auto-merge $TARGET_BRANCH into $SOURCE_BRANCH"
          else
            git checkout -b $SOURCE_BRANCH $TARGET_BRANCH
          fi
          git push -u origin $SOURCE_BRANCH

  mcore:
    uses: NVIDIA-NeMo/FW-CI-templates/.github/workflows/_bump_yamlfile.yml@v0.27.1
    needs: [update-branch]
    with:
      source-repository: NVIDIA/Megatron-LM
      source-ref: ${{ inputs.mcore-target-branch }}
      yaml-path: '."vcs-dependencies"."megatron-lm".ref'
      file: requirements/manifest.json
      base-branch: weekly-bump-${{ inputs.nemo-target-branch }}
      cicd-labels: Run CICD,no-fail-fast
      pr-reviewers: ${{ inputs.pr-reviewers }}
    secrets:
      PAT: ${{ secrets.PAT }}
