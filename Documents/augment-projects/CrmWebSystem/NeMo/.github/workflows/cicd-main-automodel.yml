# Copyright (c) 2025, NVIDIA CORPORATION.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
name: NeMo E2E Automodel Tests
on:
  workflow_call:
    inputs:
      test_to_run:
        required: true
        type: string
      image-name:
        required: false
        default: nemo_container_automodel
        type: string

jobs:
  build:
    uses: ./.github/workflows/_build_container.yml
    with:
      image-name: ${{ inputs.image-name }}
      dockerfile: docker/Dockerfile.ci

  unit-tests:
    strategy:
      fail-fast: false
      matrix:
        include:
          - script: L0_Unit_Tests_GPU_Automodel
            runner: self-hosted-azure-gpus-1
          - script: L0_Unit_Tests_CPU_Automodel
            runner: self-hosted-azure-cpu
            cpu-only: true
    needs: [build]
    runs-on: ${{ matrix.runner }}
    name: ${{ matrix.script }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          path: ${{ github.run_id }}
      - name: main
        uses: NVIDIA/NeMo/.github/actions/test-template@main
        with:
          runner: ${{ runner.name }}
          script: ${{ matrix.script }}
          is_unit_test: true
          tests_to_run: ${{ inputs.test_to_run }}
          image: ${{ inputs.image-name }}
          cpu-only: ${{ matrix.cpu-only || false }}
          is_optional: ${{ matrix.is-optional || false }}

  e2e-tests:
    strategy:
      fail-fast: false
      matrix:
        include:
          - runner: self-hosted-azure-gpus-1
            script: L2_VLM_HF_Transformer_PEFT
          - runner: self-hosted-azure
            script: L2_VLM_HF_Transformer_PEFT_FSDP2
          - runner: self-hosted-azure-gpus-1
            script: L2_VLM_HF_Transformer_PEFT_4bit
          - runner: self-hosted-azure
            script: L2_VLM_HF_Transformer_SFT_FSDP2
          - runner: self-hosted-azure
            script: L2_HF_Transformer_PEFT_notebook
          - runner: self-hosted-azure-gpus-1
            script: L2_HF_Transformer_PEFT
          - runner: self-hosted-azure-gpus-1
            script: L2_HF_Transformer_PEFT_nemorun
          - runner: self-hosted-azure
            script: L2_HF_Transformer_PEFT_2gpu
          - runner: self-hosted-azure
            script: L2_HF_Transformer_PEFT_2gpu_FSDP2_liger
          - runner: azure-gpu-vm-runner1-h100
            script: L2_HF_Transformer_PEFT_2gpu_FSDP2_fp8
          - runner: self-hosted-azure
            script: L2_HF_Transformer_PEFT_2gpu_FSDP2
          - runner: self-hosted-azure
            script: L2_HF_Transformer_PEFT_2gpu_nemorun
          - runner: self-hosted-azure
            script: L2_HF_Transformer_SFT_2gpu
          - runner: self-hosted-azure
            script: L2_HF_Transformer_SFT_2gpu_FSDP2
          - runner: azure-gpu-vm-runner1-h100
            script: L2_HF_Transformer_SFT_2gpu_FSDP2_fp8
          - runner: self-hosted-azure
            script: L2_HF_Transformer_SFT_2gpu_nemorun
          - runner: self-hosted-azure
            script: L2_HF_Transformer_SFT_2gpu_nemorun_fsdp2
          - runner: self-hosted-azure
            script: L2_HF_Transformer_SFT_FSDP2_2gpu
          - runner: self-hosted-azure
            script: L2_HF_Transformer_PT_2gpu
          - runner: self-hosted-azure
            script: L2_HF_Transformer_PT_2gpu_nemorun
          - runner: self-hosted-azure-gpus-1
            script: L2_HF_Transformer_PT
          - runner: self-hosted-azure-gpus-1
            script: L2_HF_Transformer_PT_nemorun
          - runner: self-hosted-azure
            script: L2_HF_Transformer_SFT_notebook
          - runner: self-hosted-azure-gpus-1
            script: L2_HF_Transformer_SFT
          - runner: self-hosted-azure-gpus-1
            script: L2_HF_Transformer_SFT_nemorun
          - runner: self-hosted-azure-gpus-1
            script: L2_HF_Transformer_SFT_TE_Acceleration
          - runner: self-hosted-azure-gpus-1
            script: L2_HF_Transformer_PT_TE_Acceleration
    needs: [unit-tests]
    runs-on: ${{ matrix.runner }}
    name: ${{ matrix.is-optional && 'PLEASEFIXME_' || '' }}${{ matrix.script }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          path: ${{ github.run_id }}
      - name: main
        uses: NVIDIA/NeMo/.github/actions/test-template@main
        with:
          runner: ${{ runner.name }}
          script: ${{ matrix.script }}
          tests_to_run: ${{ inputs.test_to_run }}
          image: ${{ inputs.image-name }}
          is_optional: ${{ matrix.is-optional || false }}
