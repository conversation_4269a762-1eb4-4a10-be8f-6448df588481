name: gh-docs-build
on:
  push:
  pull_request:
    paths:
      - "**"

# Set the access for individual scopes
permissions: write-all

env:
  PYTHON_VERSION: "3.11"

jobs:
  deploy:
    runs-on: ubuntu-latest

    container:
      image: squidfunk/mkdocs-material

    steps:
      - uses: actions/checkout@v4
        if: github.event.repository.fork == false
        with:
          ref: gh-pages-src

      - name: "Correct github config"
        if: github.event.repository.fork == false
        run: |
          git config --global --add safe.directory "$GITHUB_WORKSPACE"
          git config --global user.name "${GITHUB_ACTOR}"
          git config --global user.email "${GITHUB_ACTOR}@users.noreply.${GITHUB_DOMAIN:-"github.com"}"
          remote_repo="https://x-access-token:${GITHUB_TOKEN}@${GITHUB_DOMAIN:-"github.com"}/${GITHUB_REPOSITORY}.git"
          echo "${remote_repo}"
          git remote rm origin
          git remote add origin "${remote_repo}"

      - name: "Deploy Github Page"
        continue-on-error: true
        run:  mkdocs gh-deploy --force

  linkcheck:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get changed files
        id: changed-files
        uses: step-security/changed-files@v45.0.1
        with:
          files: docs/**
          files_separator: ","
          separator: " "

      - name: Set up Python ${{ env.PYTHON_VERSION }}
        if: steps.changed-files.outputs.any_changed == 'true'
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Sphinx dependencies
        if: steps.changed-files.outputs.any_changed == 'true'
        run: python3 -m pip install -r requirements/requirements_docs.txt

      - name: Linkcheck docs build
        if: steps.changed-files.outputs.any_changed == 'true'
        run: make -C docs linkcheck || true

      - name: Eliminate false positives
        if: steps.changed-files.outputs.any_changed == 'true'
        run: ./docs/check_for_broken_links.sh

      - name: Upload linkcheck output
        if: steps.changed-files.outputs.any_changed == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: linkcheck-artifact
          path: docs/build/linkcheck
          if-no-files-found: error
          retention-days: 7
