name: Cancel Workflow
description: >
  Cancels the current workflow run, i.e. all jobs. Useful if you want to cancel the rest of the workflow when one job
  fails. Note that this will cause the workflow to appear cancelled, not failed.

# Cancelling the workflow in a post-script (like this:
# https://docs.github.com/en/actions/creating-actions/metadata-syntax-for-github-actions#runspost; can also be done with
# this action: https://github.com/webiny/action-post-run, see Git history of this file) wouldn't help the status, it
# would still be cancelled. It actually indeed is, but it would be nicer to set it to failed, but there seems to be no
# way to do this.

runs:
  using: "composite"
  steps:
    - name: Cancel Workflow
      # # Fork PRs won't have a token with write access to Actions, thus won't be able to cancel the workflow.
      # if: github.event.pull_request == '' || github.event.pull_request.head.repo.fork == false
      shell: bash
      run: |
        curl --verbose \
          -X POST \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer ${{ github.token }}" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          https://api.github.com/repos/${{ github.repository }}/actions/runs/${{ github.run_id }}/cancel