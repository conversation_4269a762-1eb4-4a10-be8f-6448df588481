---
description: 
globs: 
alwaysApply: true
---
# Projectregels voor CRM Dashboard Webapp

## Doel
Een moderne webapp met:
- Publieke website
- Gebruikersregistratie & login
- Beveiligd CRM-dashboard
- Projectopvolging
- Taakbeheer per project

## Technologieën
- **Next.js 14+** (App Router, React Server Components)
- **TypeScript**
- **Tailwind CSS** + **Shadcn UI** + **Radix UI**
- **Zustand** (state management)
- **React Query (TanStack)** (data fetching)
- **Zod** (validatie)
- **PostgreSQL** of **PlanetScale** (database)
- **Prisma** (ORM)
- **Auth.js** (voorheen NextAuth)

## Structuur