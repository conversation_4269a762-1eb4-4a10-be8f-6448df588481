import { DataProvider } from '@refinedev/core';

// Custom data provider for our CRM system
export const dataProvider: DataProvider = {
  getList: async ({ resource, pagination, filters, sorters, meta }) => {
    const url = new URL(`/api/${resource}`, window.location.origin);
    
    // Add pagination
    if (pagination) {
      url.searchParams.append('page', String(pagination.current || 1));
      url.searchParams.append('limit', String(pagination.pageSize || 10));
    }
    
    // Add filters
    if (filters) {
      filters.forEach((filter) => {
        if (filter.operator === 'eq' && filter.value) {
          url.searchParams.append(filter.field, String(filter.value));
        }
      });
    }
    
    // Add sorting
    if (sorters && sorters.length > 0) {
      const sorter = sorters[0];
      url.searchParams.append('sortBy', sorter.field);
      url.searchParams.append('sortOrder', sorter.order || 'asc');
    }

    const response = await fetch(url.toString());
    const data = await response.json();

    return {
      data: data.data || data,
      total: data.total || data.length || 0,
    };
  },

  getOne: async ({ resource, id }) => {
    const response = await fetch(`/api/${resource}/${id}`);
    const data = await response.json();
    
    return {
      data,
    };
  },

  create: async ({ resource, variables }) => {
    const response = await fetch(`/api/${resource}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(variables),
    });
    
    const data = await response.json();
    
    return {
      data,
    };
  },

  update: async ({ resource, id, variables }) => {
    const response = await fetch(`/api/${resource}/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(variables),
    });
    
    const data = await response.json();
    
    return {
      data,
    };
  },

  deleteOne: async ({ resource, id }) => {
    const response = await fetch(`/api/${resource}/${id}`, {
      method: 'DELETE',
    });
    
    const data = await response.json();
    
    return {
      data,
    };
  },

  getApiUrl: () => '/api',
};

// Resource definitions for our CRM
export const resources = [
  {
    name: 'projects',
    list: '/dashboard/projects',
    create: '/dashboard/projects/new',
    edit: '/dashboard/projects/:id/edit',
    show: '/dashboard/projects/:id',
    meta: {
      canDelete: true,
    },
  },
  {
    name: 'tasks',
    list: '/dashboard/tasks',
    create: '/dashboard/tasks/new',
    edit: '/dashboard/tasks/:id/edit',
    show: '/dashboard/tasks/:id',
    meta: {
      canDelete: true,
    },
  },
  {
    name: 'projects/:id/tasks',
    list: '/dashboard/projects/:id/tasks',
    create: '/dashboard/projects/:id/tasks/new',
    edit: '/dashboard/projects/:id/tasks/:taskId/edit',
    show: '/dashboard/projects/:id/tasks/:taskId',
    meta: {
      canDelete: true,
      parent: 'projects',
    },
  },
];
