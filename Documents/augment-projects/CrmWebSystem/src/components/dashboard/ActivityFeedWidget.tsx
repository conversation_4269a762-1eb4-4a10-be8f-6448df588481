'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { CheckCircle, Clock, FileText, Plus, User } from 'lucide-react';

interface ActivityItem {
  id: string;
  type: 'task_completed' | 'project_created' | 'task_created' | 'project_updated';
  title: string;
  description: string;
  timestamp: Date;
  projectName?: string;
}

interface ActivityFeedWidgetProps {
  activities?: ActivityItem[];
  className?: string;
}

const activityIcons = {
  task_completed: CheckCircle,
  project_created: Plus,
  task_created: FileText,
  project_updated: User
};

const activityColors = {
  task_completed: 'text-green-500',
  project_created: 'text-blue-500',
  task_created: 'text-purple-500',
  project_updated: 'text-gold-500'
};

// Mock data for demonstration
const mockActivities: ActivityItem[] = [
  {
    id: '1',
    type: 'task_completed',
    title: 'Frontend development voltooid',
    description: 'Taak succesvol afgerond in Website Redesign',
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    projectName: 'Website Redesign'
  },
  {
    id: '2',
    type: 'project_created',
    title: 'Nieuw project aangemaakt',
    description: 'Mobile App Development project gestart',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    projectName: 'Mobile App Development'
  },
  {
    id: '3',
    type: 'task_created',
    title: 'Nieuwe taak toegevoegd',
    description: 'Backend API taak toegevoegd aan Website Redesign',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
    projectName: 'Website Redesign'
  },
  {
    id: '4',
    type: 'project_updated',
    title: 'Project bijgewerkt',
    description: 'Database Optimalisatie project status gewijzigd',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    projectName: 'Database Optimalisatie'
  }
];

export function ActivityFeedWidget({
  activities = mockActivities,
  className = ''
}: ActivityFeedWidgetProps) {
  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return `${diffInMinutes} minuten geleden`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} ${hours === 1 ? 'uur' : 'uren'} geleden`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `${days} ${days === 1 ? 'dag' : 'dagen'} geleden`;
    }
  };

  return (
    <Card className={`border-primary/20 hover:border-primary/30 transition-all duration-300 ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Recente Activiteit
        </CardTitle>
      </CardHeader>
      <CardContent>
        {activities.length === 0 ? (
          <p className="text-sm text-muted-foreground text-center py-4">
            Geen recente activiteit
          </p>
        ) : (
          <div className="space-y-4">
            {activities.map((activity, index) => {
              const Icon = activityIcons[activity.type];
              const colorClass = activityColors[activity.type];

              return (
                <motion.div
                  key={activity.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-start gap-3 p-3 rounded-lg hover:bg-accent/50 transition-colors"
                >
                  <div className={`flex-shrink-0 ${colorClass}`}>
                    <Icon className="h-4 w-4" />
                  </div>

                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-foreground">
                      {activity.title}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {activity.description}
                    </p>
                    {activity.projectName && (
                      <p className="text-xs text-primary mt-1">
                        {activity.projectName}
                      </p>
                    )}
                  </div>

                  <div className="flex-shrink-0">
                    <p className="text-xs text-muted-foreground">
                      {formatTimeAgo(activity.timestamp)}
                    </p>
                  </div>
                </motion.div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
