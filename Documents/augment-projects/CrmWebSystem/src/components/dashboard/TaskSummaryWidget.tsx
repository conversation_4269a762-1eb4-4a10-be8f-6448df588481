'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { AlertTriangle, Calendar, CheckSquare, Clock } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { ProgressRing } from './ProgressRing';

interface TaskSummary {
  total: number;
  completed: number;
  inProgress: number;
  todo: number;
  overdue: number;
  dueToday: number;
}

interface TaskSummaryWidgetProps {
  taskSummary?: TaskSummary;
  className?: string;
}

// Mock data for demonstration
const mockTaskSummary: TaskSummary = {
  total: 12,
  completed: 6,
  inProgress: 3,
  todo: 3,
  overdue: 1,
  dueToday: 2
};

export function TaskSummaryWidget({
  taskSummary = mockTaskSummary,
  className = ''
}: TaskSummaryWidgetProps) {
  const router = useRouter();

  const completionPercentage = taskSummary.total > 0
    ? Math.round((taskSummary.completed / taskSummary.total) * 100)
    : 0;

  const taskStats = [
    {
      label: 'Te doen',
      value: taskSummary.todo,
      color: 'bg-blue-500/20 text-blue-600 border-blue-500/30',
      icon: Clock
    },
    {
      label: 'Bezig',
      value: taskSummary.inProgress,
      color: 'bg-yellow-500/20 text-yellow-600 border-yellow-500/30',
      icon: CheckSquare
    },
    {
      label: 'Voltooid',
      value: taskSummary.completed,
      color: 'bg-green-500/20 text-green-600 border-green-500/30',
      icon: CheckSquare
    }
  ];

  return (
    <Card className={`border-primary/20 hover:border-primary/30 transition-all duration-300 ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <CheckSquare className="h-5 w-5" />
            Taken Overzicht
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/dashboard/tasks')}
            className="text-primary hover:text-primary/80"
          >
            Alle taken
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Progress Ring */}
        <div className="flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            <ProgressRing
              progress={completionPercentage}
              size={120}
              strokeWidth={10}
              color="hsl(var(--primary))"
            />
          </motion.div>
        </div>

        {/* Task Statistics */}
        <div className="grid grid-cols-3 gap-3">
          {taskStats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                className="text-center space-y-2"
              >
                <div className="flex items-center justify-center">
                  <Badge className={stat.color}>
                    <Icon className="h-3 w-3 mr-1" />
                    {stat.value}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground">
                  {stat.label}
                </p>
              </motion.div>
            );
          })}
        </div>

        {/* Urgent Tasks */}
        {(taskSummary.overdue > 0 || taskSummary.dueToday > 0) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="space-y-3 pt-4 border-t border-border"
          >
            <h4 className="text-sm font-medium text-foreground">
              Aandacht vereist
            </h4>

            {taskSummary.overdue > 0 && (
              <div className="flex items-center justify-between p-2 rounded-lg bg-red-500/10 border border-red-500/20">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <span className="text-sm text-red-600">
                    {taskSummary.overdue} overschreden
                  </span>
                </div>
                <Badge variant="destructive" className="text-xs">
                  Urgent
                </Badge>
              </div>
            )}

            {taskSummary.dueToday > 0 && (
              <div className="flex items-center justify-between p-2 rounded-lg bg-gold-500/10 border border-gold-500/20">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gold-500" />
                  <span className="text-sm text-gold-600">
                    {taskSummary.dueToday} vandaag
                  </span>
                </div>
                <Badge variant="secondary" className="text-xs">
                  Vandaag
                </Badge>
              </div>
            )}
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
}
