'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { interactiveMCP, mcpUtils } from '@/lib/interactive-mcp';
import { useList, useNavigation } from '@refinedev/core';
import { motion } from 'framer-motion';
import { Edit, Eye, Plus, Search, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface Project {
  id: string;
  name: string;
  description: string;
  status: 'ACTIVE' | 'COMPLETED' | 'ARCHIVED';
  createdAt: string;
  updatedAt: string;
  _count: {
    tasks: number;
  };
}

export function ProjectList() {
  const { show, edit, create } = useNavigation();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const { data, isLoading, refetch } = useList<Project>({
    resource: 'projects',
    pagination: {
      current: 1,
      pageSize: 20,
    },
    filters: [
      ...(searchTerm ? [{
        field: 'name',
        operator: 'contains' as const,
        value: searchTerm,
      }] : []),
      ...(statusFilter !== 'all' ? [{
        field: 'status',
        operator: 'eq' as const,
        value: statusFilter,
      }] : []),
    ],
  });

  const projects = data?.data || [];

  const handleDelete = async (project: Project) => {
    const confirmed = await interactiveMCP.confirmAction(
      `project "${project.name}" verwijderen`,
      'Deze actie kan niet ongedaan worden gemaakt. Alle taken in dit project worden ook verwijderd.'
    );

    if (!confirmed) return;

    try {
      const response = await fetch(`/api/projects/${project.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete project');

      await mcpUtils.notifySuccess(`Project "${project.name}" succesvol verwijderd`);
      refetch();
    } catch (error) {
      await mcpUtils.notifyError('Fout bij het verwijderen van het project');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-500/20 text-green-600 border-green-500/30';
      case 'COMPLETED':
        return 'bg-blue-500/20 text-blue-600 border-blue-500/30';
      case 'ARCHIVED':
        return 'bg-gray-500/20 text-gray-600 border-gray-500/30';
      default:
        return 'bg-gray-500/20 text-gray-600 border-gray-500/30';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'Actief';
      case 'COMPLETED':
        return 'Voltooid';
      case 'ARCHIVED':
        return 'Gearchiveerd';
      default:
        return status;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-muted rounded w-1/4 mb-2"></div>
              <div className="h-3 bg-muted rounded w-3/4 mb-4"></div>
              <div className="flex gap-2">
                <div className="h-6 bg-muted rounded w-16"></div>
                <div className="h-6 bg-muted rounded w-20"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Zoek projecten..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter op status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Alle statussen</SelectItem>
              <SelectItem value="ACTIVE">Actief</SelectItem>
              <SelectItem value="COMPLETED">Voltooid</SelectItem>
              <SelectItem value="ARCHIVED">Gearchiveerd</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button
          onClick={() => create('projects')}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Nieuw Project
        </Button>
      </div>

      {/* Projects Grid */}
      {projects.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">
              {searchTerm || statusFilter !== 'all'
                ? 'Geen projecten gevonden met de huidige filters'
                : 'Nog geen projecten aangemaakt'
              }
            </p>
            {!searchTerm && statusFilter === 'all' && (
              <Button
                onClick={() => create('projects')}
                className="mt-4"
                variant="outline"
              >
                <Plus className="h-4 w-4 mr-2" />
                Maak je eerste project aan
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="border-primary/20 hover:border-primary/30 transition-all duration-300 h-full">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg line-clamp-2">
                      {project.name}
                    </CardTitle>
                    <Badge className={getStatusColor(project.status)}>
                      {getStatusLabel(project.status)}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {project.description || 'Geen beschrijving'}
                  </p>

                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>{project._count.tasks} taken</span>
                    <span>
                      {new Date(project.updatedAt).toLocaleDateString('nl-NL')}
                    </span>
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => show('projects', project.id)}
                      className="flex-1"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Bekijken
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => edit('projects', project.id)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDelete(project)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
