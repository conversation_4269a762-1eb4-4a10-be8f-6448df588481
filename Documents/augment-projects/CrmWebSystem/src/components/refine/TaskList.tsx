'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { interactiveMCP, mcpUtils } from '@/lib/interactive-mcp';
import { useList, useNavigation, useUpdate } from '@refinedev/core';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import { motion } from 'framer-motion';
import {
  AlertTriangle,
  Calendar,
  CheckCircle,
  Circle,
  Clock,
  Edit,
  Eye,
  PlayCircle,
  Plus,
  Search,
  Trash2
} from 'lucide-react';
import { useState } from 'react';

interface Task {
  id: string;
  title: string;
  description: string;
  status: 'TODO' | 'IN_PROGRESS' | 'COMPLETED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  dueDate?: string;
  createdAt: string;
  updatedAt: string;
  projectId: string;
  project?: {
    id: string;
    name: string;
  };
}

interface TaskListProps {
  projectId?: string;
  showProjectColumn?: boolean;
}

export function TaskList({ projectId, showProjectColumn = true }: TaskListProps) {
  const { show, edit, create } = useNavigation();
  const { mutate: updateTask } = useUpdate();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);

  const { data, isLoading, refetch } = useList<Task>({
    resource: 'tasks',
    pagination: {
      current: 1,
      pageSize: 50,
    },
    filters: [
      ...(projectId ? [{
        field: 'projectId',
        operator: 'eq' as const,
        value: projectId,
      }] : []),
      ...(searchTerm ? [{
        field: 'title',
        operator: 'contains' as const,
        value: searchTerm,
      }] : []),
      ...(statusFilter !== 'all' ? [{
        field: 'status',
        operator: 'eq' as const,
        value: statusFilter,
      }] : []),
      ...(priorityFilter !== 'all' ? [{
        field: 'priority',
        operator: 'eq' as const,
        value: priorityFilter,
      }] : []),
    ],
    sorters: [
      {
        field: 'priority',
        order: 'desc',
      },
      {
        field: 'dueDate',
        order: 'asc',
      },
    ],
  });

  const tasks = data?.data || [];

  const handleStatusChange = async (task: Task, newStatus: Task['status']) => {
    const confirmed = await interactiveMCP.confirmAction(
      `taak status wijzigen naar "${getStatusLabel(newStatus)}"`,
      `Taak: "${task.title}"`
    );

    if (!confirmed) return;

    try {
      updateTask({
        resource: 'tasks',
        id: task.id,
        values: { status: newStatus },
      });

      await mcpUtils.notifySuccess(`Taak status bijgewerkt naar "${getStatusLabel(newStatus)}"`);
      refetch();
    } catch (error) {
      await mcpUtils.notifyError('Fout bij het bijwerken van de taak status');
    }
  };

  const handleBulkAction = async (action: 'complete' | 'delete') => {
    if (selectedTasks.length === 0) {
      await mcpUtils.notifyError('Selecteer eerst taken om een bulk actie uit te voeren');
      return;
    }

    const actionText = action === 'complete' ? 'voltooien' : 'verwijderen';
    const confirmed = await interactiveMCP.confirmAction(
      `${selectedTasks.length} taken ${actionText}`,
      `Deze actie kan niet ongedaan worden gemaakt.`
    );

    if (!confirmed) return;

    try {
      for (const taskId of selectedTasks) {
        if (action === 'complete') {
          updateTask({
            resource: 'tasks',
            id: taskId,
            values: { status: 'COMPLETED' },
          });
        } else {
          await fetch(`/api/tasks/${taskId}`, { method: 'DELETE' });
        }
      }

      await mcpUtils.notifySuccess(
        `${selectedTasks.length} taken succesvol ${action === 'complete' ? 'voltooid' : 'verwijderd'}`
      );
      setSelectedTasks([]);
      refetch();
    } catch (error) {
      await mcpUtils.notifyError(`Fout bij het ${actionText} van taken`);
    }
  };

  const handleDelete = async (task: Task) => {
    const confirmed = await interactiveMCP.confirmAction(
      `taak "${task.title}" verwijderen`,
      'Deze actie kan niet ongedaan worden gemaakt.'
    );

    if (!confirmed) return;

    try {
      const response = await fetch(`/api/tasks/${task.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete task');

      await mcpUtils.notifySuccess(`Taak "${task.title}" succesvol verwijderd`);
      refetch();
    } catch (error) {
      await mcpUtils.notifyError('Fout bij het verwijderen van de taak');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'TODO':
        return <Circle className="h-4 w-4" />;
      case 'IN_PROGRESS':
        return <PlayCircle className="h-4 w-4" />;
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Circle className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'TODO':
        return 'bg-gray-500/20 text-gray-600 border-gray-500/30';
      case 'IN_PROGRESS':
        return 'bg-blue-500/20 text-blue-600 border-blue-500/30';
      case 'COMPLETED':
        return 'bg-green-500/20 text-green-600 border-green-500/30';
      default:
        return 'bg-gray-500/20 text-gray-600 border-gray-500/30';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'TODO':
        return 'Te doen';
      case 'IN_PROGRESS':
        return 'Bezig';
      case 'COMPLETED':
        return 'Voltooid';
      default:
        return status;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return 'bg-green-500/20 text-green-600 border-green-500/30';
      case 'MEDIUM':
        return 'bg-yellow-500/20 text-yellow-600 border-yellow-500/30';
      case 'HIGH':
        return 'bg-gold-500/20 text-gold-600 border-gold-500/30';
      case 'URGENT':
        return 'bg-red-500/20 text-red-600 border-red-500/30';
      default:
        return 'bg-gray-500/20 text-gray-600 border-gray-500/30';
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return 'Laag';
      case 'MEDIUM':
        return 'Gemiddeld';
      case 'HIGH':
        return 'Hoog';
      case 'URGENT':
        return 'Urgent';
      default:
        return priority;
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return <AlertTriangle className="h-3 w-3" />;
      case 'HIGH':
        return <Clock className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const isOverdue = (dueDate?: string) => {
    if (!dueDate) return false;
    return new Date(dueDate) < new Date() && new Date(dueDate).toDateString() !== new Date().toDateString();
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-center space-x-4">
                <div className="h-4 w-4 bg-muted rounded"></div>
                <div className="flex-1">
                  <div className="h-4 bg-muted rounded w-1/3 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
                <div className="flex gap-2">
                  <div className="h-6 bg-muted rounded w-16"></div>
                  <div className="h-6 bg-muted rounded w-20"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Search, Filters and Actions */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Zoek taken..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Alle statussen</SelectItem>
                  <SelectItem value="TODO">Te doen</SelectItem>
                  <SelectItem value="IN_PROGRESS">Bezig</SelectItem>
                  <SelectItem value="COMPLETED">Voltooid</SelectItem>
                </SelectContent>
              </Select>

              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Prioriteit" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Alle prioriteiten</SelectItem>
                  <SelectItem value="URGENT">Urgent</SelectItem>
                  <SelectItem value="HIGH">Hoog</SelectItem>
                  <SelectItem value="MEDIUM">Gemiddeld</SelectItem>
                  <SelectItem value="LOW">Laag</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Button
            onClick={() => create('tasks')}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Nieuwe Taak
          </Button>
        </div>

        {/* Bulk Actions */}
        {selectedTasks.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="flex items-center gap-4 p-4 bg-primary/5 rounded-lg border border-primary/20"
          >
            <span className="text-sm font-medium">
              {selectedTasks.length} taken geselecteerd
            </span>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('complete')}
              >
                <CheckCircle className="h-4 w-4 mr-1" />
                Voltooien
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('delete')}
                className="text-destructive hover:text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Verwijderen
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setSelectedTasks([])}
              >
                Deselecteren
              </Button>
            </div>
          </motion.div>
        )}
      </div>

      {/* Tasks List */}
      {tasks.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">
              {searchTerm || statusFilter !== 'all' || priorityFilter !== 'all'
                ? 'Geen taken gevonden met de huidige filters'
                : 'Nog geen taken aangemaakt'
              }
            </p>
            {!searchTerm && statusFilter === 'all' && priorityFilter === 'all' && (
              <Button
                onClick={() => create('tasks')}
                className="mt-4"
                variant="outline"
              >
                <Plus className="h-4 w-4 mr-2" />
                Maak je eerste taak aan
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {tasks.map((task, index) => (
            <motion.div
              key={task.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <Card className={`border-primary/20 hover:border-primary/30 transition-all duration-300 ${isOverdue(task.dueDate) ? 'border-red-500/30 bg-red-500/5' : ''
                }`}>
                <CardContent className="p-4">
                  <div className="flex items-center gap-4">
                    <Checkbox
                      checked={selectedTasks.includes(task.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedTasks(prev => [...prev, task.id]);
                        } else {
                          setSelectedTasks(prev => prev.filter(id => id !== task.id));
                        }
                      }}
                    />

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-foreground line-clamp-1">
                          {task.title}
                        </h4>
                        {isOverdue(task.dueDate) && (
                          <Badge variant="destructive" className="text-xs">
                            Overschreden
                          </Badge>
                        )}
                      </div>

                      {task.description && (
                        <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                          {task.description}
                        </p>
                      )}

                      <div className="flex items-center gap-3 text-xs text-muted-foreground">
                        {showProjectColumn && task.project && (
                          <span className="flex items-center gap-1">
                            <span>Project:</span>
                            <span className="font-medium">{task.project.name}</span>
                          </span>
                        )}
                        {task.dueDate && (
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {format(new Date(task.dueDate), 'dd MMM', { locale: nl })}
                          </span>
                        )}
                        <span>
                          {format(new Date(task.updatedAt), 'dd MMM', { locale: nl })}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Badge className={getPriorityColor(task.priority)}>
                        {getPriorityIcon(task.priority)}
                        <span className="ml-1">{getPriorityLabel(task.priority)}</span>
                      </Badge>

                      <Select
                        value={task.status}
                        onValueChange={(value: Task['status']) => handleStatusChange(task, value)}
                      >
                        <SelectTrigger className="w-[120px]">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(task.status)}
                            <span className="text-xs">{getStatusLabel(task.status)}</span>
                          </div>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="TODO">
                            <div className="flex items-center gap-2">
                              <Circle className="h-4 w-4" />
                              Te doen
                            </div>
                          </SelectItem>
                          <SelectItem value="IN_PROGRESS">
                            <div className="flex items-center gap-2">
                              <PlayCircle className="h-4 w-4" />
                              Bezig
                            </div>
                          </SelectItem>
                          <SelectItem value="COMPLETED">
                            <div className="flex items-center gap-2">
                              <CheckCircle className="h-4 w-4" />
                              Voltooid
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>

                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => show('tasks', task.id)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => edit('tasks', task.id)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDelete(task)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
