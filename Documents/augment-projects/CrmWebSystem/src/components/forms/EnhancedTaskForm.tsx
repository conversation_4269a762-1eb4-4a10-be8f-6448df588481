'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { interactiveMCP, mcpUtils } from '@/lib/interactive-mcp';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import { motion } from 'framer-motion';
import { AlertTriangle, CalendarIcon, Clock, Lightbulb, Save, Sparkles } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface TaskFormData {
  title: string;
  description: string;
  status: 'TODO' | 'IN_PROGRESS' | 'COMPLETED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  dueDate?: Date;
  projectId?: string;
}

interface EnhancedTaskFormProps {
  initialData?: Partial<TaskFormData>;
  onSubmit?: (data: TaskFormData) => Promise<void>;
  isEditing?: boolean;
  projectId?: string;
  projects?: Array<{ id: string; name: string }>;
}

export function EnhancedTaskForm({
  initialData,
  onSubmit,
  isEditing = false,
  projectId,
  projects = []
}: EnhancedTaskFormProps) {
  const router = useRouter();
  const [formData, setFormData] = useState<TaskFormData>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    status: initialData?.status || 'TODO',
    priority: initialData?.priority || 'MEDIUM',
    dueDate: initialData?.dueDate,
    projectId: projectId || initialData?.projectId || '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  const [showCalendar, setShowCalendar] = useState(false);

  const handleInputChange = (field: keyof TaskFormData, value: string | Date) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const generateAISuggestions = async () => {
    if (!formData.title) {
      await mcpUtils.notifyError('Voer eerst een taaknaam in om suggesties te krijgen');
      return;
    }

    setIsLoading(true);
    try {
      // AI-powered task suggestions based on title and context
      const suggestions = [
        `Onderzoek en analyseer de requirements voor "${formData.title}"`,
        `Maak een gedetailleerd plan voor de implementatie van "${formData.title}"`,
        `Test en valideer de functionaliteit van "${formData.title}"`,
        `Documenteer de resultaten en bevindingen van "${formData.title}"`,
        `Review en optimaliseer de performance van "${formData.title}"`,
        `Implementeer error handling en edge cases voor "${formData.title}"`,
      ];

      setAiSuggestions(suggestions);
      await mcpUtils.notifySuccess('AI suggesties gegenereerd!');
    } catch (error) {
      await mcpUtils.notifyError('Fout bij het genereren van suggesties');
    } finally {
      setIsLoading(false);
    }
  };

  const suggestDueDate = async () => {
    const priority = formData.priority;
    let suggestedDays = 7; // Default

    switch (priority) {
      case 'URGENT':
        suggestedDays = 1;
        break;
      case 'HIGH':
        suggestedDays = 3;
        break;
      case 'MEDIUM':
        suggestedDays = 7;
        break;
      case 'LOW':
        suggestedDays = 14;
        break;
    }

    const suggestedDate = new Date();
    suggestedDate.setDate(suggestedDate.getDate() + suggestedDays);

    const confirmed = await interactiveMCP.confirmAction(
      'voorgestelde deadline gebruiken',
      `Gebaseerd op prioriteit "${priority}": ${format(suggestedDate, 'dd MMMM yyyy', { locale: nl })}`
    );

    if (confirmed) {
      setFormData(prev => ({ ...prev, dueDate: suggestedDate }));
      await mcpUtils.notifySuccess('Deadline ingesteld!');
    }
  };

  const selectSuggestion = async (suggestion: string) => {
    const confirmed = await interactiveMCP.confirmAction(
      'deze suggestie gebruiken',
      `Suggestie: "${suggestion}"`
    );

    if (confirmed) {
      setFormData(prev => ({
        ...prev,
        description: prev.description
          ? `${prev.description}\n\n${suggestion}`
          : suggestion
      }));
      await mcpUtils.notifySuccess('Suggestie toegevoegd aan beschrijving');
    }
  };

  const validateForm = async (): Promise<boolean> => {
    if (!formData.title.trim()) {
      await mcpUtils.notifyError('Taaknaam is verplicht');
      return false;
    }

    if (!formData.projectId && projects.length > 0) {
      const shouldContinue = await interactiveMCP.confirmAction(
        'doorgaan zonder project',
        'Deze taak wordt niet gekoppeld aan een project. Weet je zeker dat je wilt doorgaan?'
      );
      if (!shouldContinue) return false;
    }

    if (formData.dueDate && formData.dueDate < new Date()) {
      const shouldContinue = await interactiveMCP.confirmAction(
        'deadline in het verleden instellen',
        'De gekozen deadline ligt in het verleden. Weet je zeker dat dit correct is?'
      );
      if (!shouldContinue) return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const isValid = await validateForm();
    if (!isValid) return;

    const confirmed = await mcpUtils.confirmSave(
      isEditing ? 'taak wijzigingen' : 'nieuwe taak'
    );

    if (!confirmed) return;

    setIsLoading(true);
    try {
      if (onSubmit) {
        await onSubmit(formData);
      } else {
        // Default API call
        const endpoint = isEditing
          ? `/api/projects/${formData.projectId}/tasks/${(initialData as any)?.id}`
          : `/api/projects/${formData.projectId}/tasks`;

        const response = await fetch(endpoint, {
          method: isEditing ? 'PATCH' : 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData),
        });

        if (!response.ok) throw new Error('Failed to save task');
      }

      await mcpUtils.notifySuccess(
        isEditing ? 'Taak succesvol bijgewerkt!' : 'Taak succesvol aangemaakt!'
      );

      router.push(formData.projectId ? `/dashboard/projects/${formData.projectId}` : '/dashboard/tasks');
    } catch (error) {
      await mcpUtils.notifyError('Fout bij het opslaan van de taak');
    } finally {
      setIsLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'text-red-600';
      case 'HIGH':
        return 'text-gold-600';
      case 'MEDIUM':
        return 'text-yellow-600';
      case 'LOW':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return <AlertTriangle className="h-4 w-4" />;
      case 'HIGH':
        return <Clock className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="max-w-4xl mx-auto space-y-6"
    >
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            {isEditing ? 'Taak Bewerken' : 'Nieuwe Taak Aanmaken'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="title">Taaknaam *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Voer taaknaam in..."
                  required
                />
              </div>

              {projects.length > 0 && (
                <div className="space-y-2">
                  <Label htmlFor="project">Project</Label>
                  <Select
                    value={formData.projectId}
                    onValueChange={(value) => handleInputChange('projectId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecteer project" />
                    </SelectTrigger>
                    <SelectContent>
                      {projects.map((project) => (
                        <SelectItem key={project.id} value={project.id}>
                          {project.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>

            <div className="grid gap-6 md:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: 'TODO' | 'IN_PROGRESS' | 'COMPLETED') =>
                    handleInputChange('status', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="TODO">Te doen</SelectItem>
                    <SelectItem value="IN_PROGRESS">Bezig</SelectItem>
                    <SelectItem value="COMPLETED">Voltooid</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">Prioriteit</Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT') =>
                    handleInputChange('priority', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="LOW">
                      <span className="flex items-center gap-2">
                        <span className="text-green-600">Laag</span>
                      </span>
                    </SelectItem>
                    <SelectItem value="MEDIUM">
                      <span className="flex items-center gap-2">
                        <span className="text-yellow-600">Gemiddeld</span>
                      </span>
                    </SelectItem>
                    <SelectItem value="HIGH">
                      <span className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-gold-600" />
                        <span className="text-gold-600">Hoog</span>
                      </span>
                    </SelectItem>
                    <SelectItem value="URGENT">
                      <span className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        <span className="text-red-600">Urgent</span>
                      </span>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="dueDate">Deadline</Label>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={suggestDueDate}
                    className="text-xs"
                  >
                    Suggereer
                  </Button>
                </div>
                <Popover open={showCalendar} onOpenChange={setShowCalendar}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.dueDate ? (
                        format(formData.dueDate, 'dd MMM yyyy', { locale: nl })
                      ) : (
                        <span>Selecteer datum</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.dueDate}
                      onSelect={(date) => {
                        handleInputChange('dueDate', date || new Date());
                        setShowCalendar(false);
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="description">Beschrijving</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={generateAISuggestions}
                  disabled={isLoading || !formData.title}
                  className="flex items-center gap-2"
                >
                  <Lightbulb className="h-4 w-4" />
                  AI Suggesties
                </Button>
              </div>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Beschrijf de taak..."
                rows={6}
              />
            </div>

            <div className="flex gap-4">
              <Button
                type="submit"
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {isLoading ? 'Opslaan...' : isEditing ? 'Bijwerken' : 'Aanmaken'}
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
              >
                Annuleren
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* AI Suggestions Panel */}
      {aiSuggestions.length > 0 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3 }}
        >
          <Card className="border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Lightbulb className="h-5 w-5" />
                AI Suggesties voor Taakbeschrijving
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {aiSuggestions.map((suggestion, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-3 rounded-lg border border-border hover:border-primary/30 transition-colors cursor-pointer"
                    onClick={() => selectSuggestion(suggestion)}
                  >
                    <p className="text-sm">{suggestion}</p>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </motion.div>
  );
}
