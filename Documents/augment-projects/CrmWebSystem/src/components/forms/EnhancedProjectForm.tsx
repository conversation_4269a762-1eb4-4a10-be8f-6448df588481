'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { interactiveMCP, mcpUtils } from '@/lib/interactive-mcp';
import { motion } from 'framer-motion';
import { Lightbulb, Save, Sparkles } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface ProjectFormData {
  name: string;
  description: string;
  status: 'ACTIVE' | 'COMPLETED' | 'ARCHIVED';
}

interface EnhancedProjectFormProps {
  initialData?: Partial<ProjectFormData>;
  onSubmit?: (data: ProjectFormData) => Promise<void>;
  isEditing?: boolean;
}

export function EnhancedProjectForm({
  initialData,
  onSubmit,
  isEditing = false
}: EnhancedProjectFormProps) {
  const router = useRouter();
  const [formData, setFormData] = useState<ProjectFormData>({
    name: initialData?.name || '',
    description: initialData?.description || '',
    status: initialData?.status || 'ACTIVE',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);

  const handleInputChange = (field: keyof ProjectFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const generateAISuggestions = async () => {
    if (!formData.name) {
      await mcpUtils.notifyError('Voer eerst een projectnaam in om suggesties te krijgen');
      return;
    }

    setIsLoading(true);
    try {
      // Call AI service for real suggestions
      const response = await fetch('/api/ai/project-suggestions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectName: formData.name,
          context: formData.description
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate AI suggestions');
      }

      const aiSuggestions = await response.json();
      const suggestions = aiSuggestions.map((suggestion: any) => suggestion.description);

      setAiSuggestions(suggestions);
      await mcpUtils.notifySuccess('AI suggesties gegenereerd!');
    } catch (error) {
      console.error('AI suggestions error:', error);
      // Fallback to static suggestions
      const fallbackSuggestions = [
        `Implementeer een moderne ${formData.name} met React en TypeScript`,
        `Ontwikkel een responsive design voor ${formData.name}`,
        `Integreer database functionaliteit voor ${formData.name}`,
        `Voeg authenticatie toe aan ${formData.name}`,
        `Optimaliseer performance van ${formData.name}`,
      ];
      setAiSuggestions(fallbackSuggestions);
      await mcpUtils.notifySuccess('Suggesties gegenereerd (fallback mode)');
    } finally {
      setIsLoading(false);
    }
  };

  const selectSuggestion = async (suggestion: string) => {
    const confirmed = await interactiveMCP.confirmAction(
      'deze suggestie gebruiken',
      `Suggestie: "${suggestion}"`
    );

    if (confirmed) {
      setFormData(prev => ({
        ...prev,
        description: prev.description
          ? `${prev.description}\n\n${suggestion}`
          : suggestion
      }));
      await mcpUtils.notifySuccess('Suggestie toegevoegd aan beschrijving');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      await mcpUtils.notifyError('Projectnaam is verplicht');
      return;
    }

    const confirmed = await mcpUtils.confirmSave(
      isEditing ? 'project wijzigingen' : 'nieuw project'
    );

    if (!confirmed) return;

    setIsLoading(true);
    try {
      if (onSubmit) {
        await onSubmit(formData);
      } else {
        // Default API call
        const response = await fetch('/api/projects', {
          method: isEditing ? 'PATCH' : 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData),
        });

        if (!response.ok) throw new Error('Failed to save project');
      }

      await mcpUtils.notifySuccess(
        isEditing ? 'Project succesvol bijgewerkt!' : 'Project succesvol aangemaakt!'
      );

      router.push('/dashboard/projects');
    } catch (error) {
      await mcpUtils.notifyError('Fout bij het opslaan van het project');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="max-w-4xl mx-auto space-y-6"
    >
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            {isEditing ? 'Project Bewerken' : 'Nieuw Project Aanmaken'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">Projectnaam *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Voer projectnaam in..."
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: 'ACTIVE' | 'COMPLETED' | 'ARCHIVED') =>
                    handleInputChange('status', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ACTIVE">Actief</SelectItem>
                    <SelectItem value="COMPLETED">Voltooid</SelectItem>
                    <SelectItem value="ARCHIVED">Gearchiveerd</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="description">Beschrijving</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={generateAISuggestions}
                  disabled={isLoading || !formData.name}
                  className="flex items-center gap-2"
                >
                  <Lightbulb className="h-4 w-4" />
                  AI Suggesties
                </Button>
              </div>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Beschrijf je project..."
                rows={6}
              />
            </div>

            <div className="flex gap-4">
              <Button
                type="submit"
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {isLoading ? 'Opslaan...' : isEditing ? 'Bijwerken' : 'Aanmaken'}
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
              >
                Annuleren
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* AI Suggestions Panel */}
      {aiSuggestions.length > 0 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3 }}
        >
          <Card className="border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Lightbulb className="h-5 w-5" />
                AI Suggesties
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {aiSuggestions.map((suggestion, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-3 rounded-lg border border-border hover:border-primary/30 transition-colors cursor-pointer"
                    onClick={() => selectSuggestion(suggestion)}
                  >
                    <p className="text-sm">{suggestion}</p>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </motion.div>
  );
}
